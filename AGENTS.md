# Repository Guidelines

## Project Structure & Module Organization
- Core service code lives in `src/main/java/com/tmb/oneapp/settingexp`, grouped by layer: `controller/v1`, `service`, `client`, `model`, `config`, and `utils`. Align new classes with this package layout, creating new subpackages only when the boundary is clear.
- API contracts and static assets are under `src/main/resources`. Application configs live beside the `application-*.yml` files; keep secrets out of source.
- Tests mirror production packages in `src/test/java/com/tmb/oneapp/settingexp/...`. Place JSON fixtures in `src/test/resources` if needed.

## Build, Test, and Development Commands
- `./gradlew clean build` compiles the service, runs unit tests, and packages the Spring Boot jar.
- `./gradlew test` runs the JUnit 5 test suite; use during TDD cycles.
- `./gradlew bootRun` starts the service locally with the default profile.
- `./gradlew jacocoTestReport` produces coverage reports in `build/reports/jacoco`. Review HTML output before merging.
- `./gradlew docker` assembles the production image (depends on `bootJar`).
- `make commit` executes the test suite and opens an interactive `git commit`.

## Coding Style & Naming Conventions
- Target Java 17; prefer Spring Boot idioms (`@RestController`, `@Service`) and constructor injection.
- Use 4-space indentation, `UpperCamelCase` for classes, and `lowerCamelCase` for fields and methods.
- Data transfer models belong in `model/...` with `*Request`/`*Response` suffixes. Constants live in `constant/` and should be `UPPER_SNAKE_CASE`.
- Leverage Lombok annotations already in place; avoid mixing Lombok with handwritten boilerplate unless necessary.

## Testing Guidelines
- Unit tests use JUnit 5 and Mockito (`@ExtendWith(MockitoExtension.class)`). Name files `*Test` and mirror the package of the class under test.
- Mock external dependencies such as Feign clients and Kafka producers; assert both success and failure paths, as in `EventServiceTest`.
- Keep coverage at or above the current Jacoco baseline; fail fast if new code lacks tests.

## Commit & Pull Request Guidelines
- Follow the observed format `type(SCOPE): action` or the transactional emoji style (`🔄 Replace ...`). Always include the ONEAPP ticket (`feat(ONEAPP-123456)`).
- Limit commits to a cohesive change set; run `./gradlew test` before pushing.
- Pull requests must describe the change, note affected endpoints, and link the Jira ticket. Attach screenshots or sample payloads when the API contract changes.

## Environment & Security Notes
- Repository access to the internal Nexus is configured via `gradle.properties`. Override credentials through `-PrepoUsername`/`-PrepoPassword` when building locally.
- The Docker image expects runtime secrets via environment variables; never commit credentials or keystore passwords.
