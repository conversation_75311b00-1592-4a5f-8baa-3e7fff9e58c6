## Project Overview

This is a Spring Boot microservice named `one_setting-exp`. Based on the file structure and dependencies, it appears to be a backend service within a larger "OneApp" ecosystem, likely related to user settings and configurations.

**Key Technologies:**

*   **Backend:** Java 17, Spring Boot
*   **Build:** Gradle
*   **Dependencies:**
    *   Spring Web, Spring Cloud OpenFeign (for declarative REST clients)
    *   Project Lombok
    *   TMB-specific common libraries (`tmb_common_utility`, `oneapp-redis-client-lib`, etc.)
    *   Testing with JUnit 5 and Mockito.
    *   JaCoCo for code coverage.
    *   SonarQube for static code analysis.
*   **Containerization:** Docker

## Building and Running

### Building the project

To build the project and create a JAR file, run the following command:

```bash
./gradlew build
```

### Running the application

You can run the application using the Spring Boot Gradle plugin:

```bash
./gradlew bootRun
```

### Running tests

To run the unit tests, execute:

```bash
./gradlew test
```

## Development Conventions

*   **Service-Oriented Architecture:** The project uses a service-oriented architecture, with `Service` and `ServiceImpl` classes encapsulating business logic.
*   **Feign Clients:** The application communicates with other services using Spring Cloud OpenFeign.
*   **Planning Documents:** The presence of `pin_free_service_plan.md` suggests that development work is planned and documented before implementation. These plans include details on architecture, data mapping, error handling, and testing.
*   **Testing:** The project has a dedicated `test` source folder with unit tests for controllers and services. The `build.gradle` file is configured to generate JaCoCo test reports.
*   **Code Quality:** SonarQube is integrated into the build process to maintain code quality.
