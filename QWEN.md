# QWEN.md - one_setting-exp Project Documentation

## Project Overview

This is a Spring Boot microservice named `one_setting-exp`. Based on the file structure and dependencies, it appears to be a backend service within a larger "OneApp" ecosystem, likely related to user settings and configurations, particularly focused on PIN-free transaction functionality.

**Key Technologies:**
* **Backend:** Java 17, Spring Boot 3.5.3
* **Build:** Gradle
* **Dependencies:**
  * Spring Web, Spring Cloud OpenFeign (for declarative REST clients)
  * Project Lombok (for reducing boilerplate code in models and DTOs)
  * TMB-specific common libraries (`tmb_common_utility`, `oneapp-redis-client-lib`, etc.)
  * Testing with JUnit 5 and Mockito
  * JaCoCo for code coverage
  * SonarQube for static code analysis
* **Containerization:** Docker

## Main Functionality

The primary function of this service is to provide PIN-free transaction configuration data for TMB's OneApp. The service integrates with:
1. CommonServiceClient - to retrieve common configuration data
2. CustomerServiceClient - to fetch customer CRM profile data

The service maps customer profile data to PIN-free response data, enabling PIN-free transactions with appropriate limits and settings based on customer profile.

## Key Components

### Controllers
- `PinFreeController` - Exposes `/pin-free` endpoint that returns PIN-free configuration data

### Services
- `PinFreeService` / `PinFreeServiceImpl` - Main business logic for fetching and mapping PIN-free data

### Clients
- `CommonServiceClient` - Feign client for common service configuration
- `CustomerServiceClient` - Feign client for customer CRM profile data

### Models
- `CustomerCrmProfile` - Represents customer CRM profile data
- `PinFreeResponse` - Response model for PIN-free configuration
- `CommonAuthenticationInformation` - Authentication information model

## Building and Running

### Building the project

To build the project and create a JAR file, run the following command:

```bash
./gradlew build
```

### Running the application

You can run the application using the Spring Boot Gradle plugin:

```bash
./gradlew bootRun
```

### Running tests

To run the unit tests, execute:

```bash
./gradlew test
```

## Development Conventions

* **Service-Oriented Architecture:** The project uses a service-oriented architecture, with `Service` and `ServiceImpl` classes encapsulating business logic.
* **Feign Clients:** The application communicates with other services using Spring Cloud OpenFeign.
* **Lombok Usage:** Project Lombok is used extensively throughout the codebase to reduce boilerplate code. Annotations like `@Getter`, `@Setter`, `@AllArgsConstructor`, `@NoArgsConstructor`, and `@ToString` are used in model classes to automatically generate getters, setters, constructors, and toString methods.
* **Planning Documents:** The presence of `pin_free_service_plan.md` suggests that development work is planned and documented before implementation. These plans include details on architecture, data mapping, error handling, and testing.
* **Testing:** The project has a dedicated `test` source folder with unit tests for controllers and services. The `build.gradle` file is configured to generate JaCoCo test reports.
* **Code Quality:** SonarQube is integrated into the build process to maintain code quality.

## Important Notes

* Lombok is used throughout model classes (e.g., `CustomerCrmProfile`, `PinFreeResponse`, `CommonAuthenticationInformation`) to automatically generate getters, setters, constructors, and other boilerplate code using annotations like `@Getter`, `@Setter`, `@AllArgsConstructor`, `@NoArgsConstructor`, and `@ToString`.
* The project follows a pattern where services make parallel calls to multiple external services using async utilities.
* Error handling is implemented with logging and fallback strategies.
* The service uses correlation IDs and CRM IDs passed through request headers for tracking and data retrieval.