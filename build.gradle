buildscript {
	ext {
		springBootVersion = '3.5.3'
	}
	repositories {
		maven {
			url 'https://nexus.tmbbank.local:8081/repository/oneapp'
			credentials {
				username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
				password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
			}
		}
		maven {
			url "https://nexus.tmbbank.local:8081/repository/plugins.gradle/"
			credentials {
				username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
				password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
			}
		}
	}

	dependencies {
		classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
		classpath "com.palantir.gradle.docker:gradle-docker:0.35.0"
		classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:4.4.1.3373"
	}
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'com.palantir.docker'
apply plugin: 'jacoco'
apply plugin: org.sonarqube.gradle.SonarQubePlugin
group = 'com.tmb.oneapp'
version = '0.0.1'
sourceCompatibility = '17'

if (project.hasProperty('projVersion')) {
	project.version = project.projVersion
} else {
	project.version = '15.0.0'
}

test {
	useJUnitPlatform()
}


configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}
repositories {
	maven {
		url 'https://nexus.tmbbank.local:8081/repository/oneapp'
		credentials {
			username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
			password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
		}
	}
}

springBoot {
	buildInfo()
}

ext {
	set('springCloudVersion', "2025.0.0")
	set('log4j2.version',"2.17.1")
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-aop'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	implementation 'com.tmb.common:tmb_common_utility:3.3.0-dev.4'
	implementation 'com.tmb.common:oneapp-redis-client-lib:3.2.0-rc.2'
	implementation 'com.tmb.common:one-kafka-lib:1.1.0-rc.2'
	implementation 'com.tmb.oneapp:body-decryption-lib:3.1.0-rc.2'
	implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
	implementation 'org.springframework.kafka:spring-kafka'
	implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'
	implementation 'org.apache.commons:commons-text:1.10.0'
	implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.14.0'
	testImplementation 'org.junit.jupiter:junit-jupiter-api'
	testImplementation 'org.junit.jupiter:junit-jupiter-engine'
	testImplementation 'org.junit.vintage:junit-vintage-engine'
	testImplementation 'org.junit.platform:junit-platform-launcher'
	testImplementation 'org.junit.platform:junit-platform-runner'
	testImplementation('org.springframework.boot:spring-boot-starter-test') {
		exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
	}
	implementation group: 'org.springdoc', name: 'springdoc-openapi-starter-webmvc-ui', version: '2.3.0'
	implementation 'org.json:json:20231013'
	implementation('io.micrometer:micrometer-registry-prometheus')
	implementation group: 'org.apache.commons', name: 'commons-pool2', version: '2.11.1'
	testImplementation 'org.mockito:mockito-inline:3.9.0'
	implementation 'org.mapstruct:mapstruct:1.5.3.Final'
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
	implementation group: 'commons-io', name: 'commons-io', version: '2.15.1'
	implementation 'com.google.guava:guava:32.1.3-jre'
	implementation group: 'io.netty', name: 'netty-handler', version: '4.1.118.Final'
}

dependencyManagement {
	imports {
		mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
	}
}

tasks.named('test') {
	useJUnitPlatform()
}

test {
	useJUnitPlatform()
}

jar {
	enabled = false
	archiveClassifier = ''
}

docker {
	name "com.tmb.oneapp/${project.name}:${project.version}"
	dockerfile file('Dockerfile')
	files jar.archiveFile
	buildArgs(['JAR_FILE': "${jar.archiveFileName}"])
}
tasks.getByPath('dockerPrepare').dependsOn('bootJar')
tasks.getByPath('dockerPrepare').dependsOn('jar')
tasks.getByPath('docker').dependsOn('build')

jacoco {
	toolVersion = "0.8.11"
}


jacocoTestReport {
	reports {
		html.required = true
		xml.required = true
		csv.required = true
	}
}

sonarqube {
	if (System.getProperty("sonar.host.url").equals(null)){
		properties {
			System.setProperty('sonar.host.url', 'http://localhost:9000')
		}
	}
	properties {
		property 'sonar.coverage.exclusions', '**/interfaceobject/**,**/config/*,**/model/**,**/data/*,**/constant/*,**/utils/*, **/CommonServiceApplication.java,**/CommonServiceUtils.java, **/SettingExpApplication.java'
	}
	properties {
		property 'sonar.exclusions', '**/interfaceobject/**,**/config/*, **/SettingExpApplication.java, **/data/** , **/model/**'
	}
}
test.finalizedBy jacocoTestReport
