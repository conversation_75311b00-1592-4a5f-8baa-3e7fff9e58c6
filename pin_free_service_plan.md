# Pin-Free Service Implementation Plan

## Overview
The goal is to refactor the `PinFreeController` to use actual service calls instead of hardcoded values. The controller will call:
1. `getCommonConfigByModule` from `CommonServiceClient` to get configuration data
2. `fetchCustomerCrmProfile` from `CustomerServiceClient` to get customer profile data

## Current Implementation Analysis

### Current PinFreeController
- Returns hardcoded values:
  - `pinFreeMaxLimit`: "5000"
  - `pinFreeMaxAccum`: "5000"
  - `pinFreeEnableFlag`: true
  - `pinFreeQrLimit`: "1000"
  - `isRequireCommonAuthen`: true
  - `CommonAuthenticationInformation` with hardcoded values

### Target Implementation
- Call external services to get actual data
- Map `CustomerCrmProfile` fields to `PinFreeResponse`
- Handle error scenarios and fallbacks

## Data Mapping

### CustomerCrmProfile → PinFreeResponse Mapping

| CustomerCrmProfile Field | PinFreeResponse Field | Data Type |
|--------------------------|----------------------|-----------|
| `pinFreeSeetingFlag` | `pinFreeEnableFlag` | Boolean |
| `pinFreeTrLimit` | `pinFreeMaxLimit` | String |
| `pinFreeBpLimit` | `pinFreeMaxAccum` | String |
| `pinFreeQrLimit` | `pinFreeQrLimit` | String |
| `pinFreeAccuUsgAmt` | `totalPaymentAccumulateUsage` | String |
| `pinFreeTxnCount` | `dailyAmount` | String |

## Architecture Design

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PinFreeController  │───►│  PinFreeService  │───►│   External APIs │
│                 │    │                  │    │                 │
│ - /pin-free     │    │ - getPinFreeData │    │ - CommonService │
│ - Request/Response │    │ - mapData        │    │ - CustomerService │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Implementation Steps

### 1. Add Constants
- Add `COMMON_MODULE = "common_module"` to `SettingExpConstant.java`

### 2. Create PinFreeService
- Create `PinFreeService` interface in `service` package
- Create `PinFreeServiceImpl` implementation
- Inject `CommonServiceClient` and `CustomerServiceClient`
- Implement `getPinFreeData(correlationId, crmId)` method

### 3. Update PinFreeController
- Inject `PinFreeService`
- Replace hardcoded logic with service call
- Handle service responses and errors

### 4. Data Processing Logic

#### getCommonConfigByModule Call
```java
// Search for "common_module" configuration
ResponseEntity<TmbServiceResponse<List<CommonData>>> response =
    commonServiceClient.getCommonConfigByModule("common_module");
```

#### fetchCustomerCrmProfile Call
```java
// Get customer profile data
ResponseEntity<TmbServiceResponse<CustomerCrmProfile>> profileResponse =
    customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId);
```

#### Data Mapping Logic
```java
PinFreeResponse response = new PinFreeResponse();
CustomerCrmProfile profile = profileResponse.getBody().getData();

// Map customer profile to pin-free response
response.setPinFreeEnableFlag(Boolean.valueOf(profile.getPinFreeSeetingFlag()));
response.setPinFreeMaxLimit(String.valueOf(profile.getPinFreeTrLimit()));
response.setPinFreeMaxAccum(String.valueOf(profile.getPinFreeBpLimit()));
response.setPinFreeQrLimit(String.valueOf(profile.getPinFreeQrLimit()));

// Set common authentication info (fixed values as per requirement)
CommonAuthenticationInformation authInfo = new CommonAuthenticationInformation();
authInfo.setFeatureId("1039");
authInfo.setTotalPaymentAccumulateUsage("0.00"); // Fixed value
authInfo.setFlow("Setting PIN free");
authInfo.setDailyAmount("0.00"); // Fixed value

response.setCommonAuthenticationInformation(authInfo);
```

## Error Handling Strategy

### Service Call Failures
- If `getCommonConfigByModule` fails → Log error, continue with customer profile data
- If `fetchCustomerCrmProfile` fails → Return error response with appropriate status code
- If both fail → Return fallback response or error

### Data Validation
- Validate required fields from customer profile
- Handle null/empty values appropriately
- Set sensible defaults for missing data

## Logging Requirements
- Log incoming requests with correlation ID and CRM ID
- Log service call attempts and responses
- Log data mapping operations
- Log any errors or fallbacks used

## Testing Strategy
- Unit tests for `PinFreeServiceImpl`
- Integration tests for service calls
- Mock external service responses
- Test error scenarios and fallbacks

## Dependencies
- Spring Boot Web
- Spring Cloud OpenFeign
- TMB Common libraries
- Existing model classes (already available)

## Files to Create/Modify

### New Files
- `src/main/java/com/tmb/oneapp/settingexp/service/PinFreeService.java`
- `src/main/java/com/tmb/oneapp/settingexp/service/PinFreeServiceImpl.java`
- `src/test/java/com/tmb/oneapp/settingexp/service/PinFreeServiceImplTest.java`

### Modified Files
- `src/main/java/com/tmb/oneapp/settingexp/controller/v1/PinFreeController.java`
- `src/main/java/com/tmb/oneapp/settingexp/constant/SettingExpConstant.java`
- `src/test/java/com/tmb/oneapp/settingexp/controller/v1/PinFreeControllerTest.java`

## Success Criteria
- ✅ Controller uses actual service calls instead of hardcoded values
- ✅ Proper error handling and logging implemented
- ✅ Data correctly mapped from CustomerCrmProfile to PinFreeResponse
- ✅ All existing tests pass
- ✅ New unit tests created and passing
- ✅ API contract remains unchanged for consumers

## Rollback Plan
- If issues arise, can revert to hardcoded values
- Service calls can be disabled with feature flags
- Fallback responses can be configured

---
*This plan ensures a robust, maintainable implementation that follows Spring Boot best practices and the existing codebase patterns.*