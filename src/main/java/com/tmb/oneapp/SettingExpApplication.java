package com.tmb.oneapp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@ComponentScan(basePackages = { "com.tmb.oneapp", "com.tmb.common" })
@EnableFeignClients(defaultConfiguration = com.tmb.common.filter.FeignCommonConfig.class)
@EnableAsync
public class SettingExpApplication {

	public static void main(String [] args) {
		SpringApplication.run(SettingExpApplication.class);
	}

}
