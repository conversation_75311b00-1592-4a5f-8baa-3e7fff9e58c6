package com.tmb.oneapp.settingexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.BranchDataModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;


@FeignClient(name = "${feign.bank.service.name}", url = "${feign.bank.service.url}")
public interface BankServiceClient {

    @PostMapping(value = "/v1/bank-service/branches", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<List<BranchDataModel>>> getBranch(
            @RequestHeader(SettingExpConstant.HEADER_CORRELATION_ID) String correlationId);
}
