package com.tmb.oneapp.settingexp.client;

import org.springframework.http.MediaType;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.CommonData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name = "${feign.common.service.name}", url = "${feign.common.service.url}")
public interface CommonServiceClient {
    @GetMapping(value = {"/v1/common-service/configuration"}, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<List<CommonData>>> getCommonConfigByModule(
            @RequestParam("search") String search
    );
}