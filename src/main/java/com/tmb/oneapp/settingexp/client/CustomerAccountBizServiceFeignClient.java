package com.tmb.oneapp.settingexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.AccountSaving;
import com.tmb.oneapp.settingexp.model.AccountSortOrderRequest;
import com.tmb.oneapp.settingexp.model.AddDepositAccountRequest;
import com.tmb.oneapp.settingexp.model.manageviewonly.UpdateAccountViewOnlyRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(name = "${feign.customer.account.biz.service.name}", url = "${feign.customer.account.biz.service.url}")
public interface CustomerAccountBizServiceFeignClient {
    @GetMapping(value = "/v1/customer-account-biz/accounts-list", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<AccountSaving>> getAccountsSaving(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(value = SettingExpConstant.LIST_ALL_ACCOUNT_FLAG) String listAllAccountFlag,
            @RequestHeader(value = SettingExpConstant.REFRESH_FLAG) String refreshFlag);

    @PutMapping(value = "/v1/customer-account-biz/manage-account/reorder", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> updateReorderAccount(
            @RequestBody List<AccountSortOrderRequest> request,
            @RequestHeader HttpHeaders headers);

    @PostMapping(value = "/v1/customer-account-biz/manage-account/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> addDepositAccount(
            @RequestBody AddDepositAccountRequest request,
            @RequestHeader HttpHeaders headers);

    @PutMapping(value = "/v1/customer-account-biz/manage-account/enable-view-only", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> enableViewOnly(
            @RequestBody UpdateAccountViewOnlyRequest request,
            @RequestHeader HttpHeaders headers);
}
