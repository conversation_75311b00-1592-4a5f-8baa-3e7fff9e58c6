package com.tmb.oneapp.settingexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.CustomerCrmProfile;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRVerifyFRRequest;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRVerifyFRResponse;
import com.tmb.oneapp.settingexp.model.pinfree.UpdatePinFreeSettingsRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;


@FeignClient(name = "${feign.customers.service.name}", url = "${feign.customers.service.url}")
public interface CustomerServiceClient {
    @PostMapping(value = "/v1/customers-service/common-fr/verify-fr")
    ResponseEntity<TmbServiceResponse<CommonFRVerifyFRResponse>> postCommonFRVerifyFR(
            @RequestHeader(SettingExpConstant.HEADER_CRM_ID) String crmId,
            @RequestHeader(SettingExpConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(SettingExpConstant.HEADER_ACCEPT_LANGUAGE) String acceptLanguage,
            @RequestHeader(SettingExpConstant.HEADER_APP_VERSION) String appVersion,
            @RequestHeader(SettingExpConstant.HEADER_IP_ADDRESS) String ipAddress,
            @RequestBody CommonFRVerifyFRRequest request
    );

    @GetMapping(value = {"/apis/customers/crmprofile"}, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CustomerCrmProfile>> fetchCustomerCrmProfile(
            @RequestHeader(value = SettingExpConstant.HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = SettingExpConstant.HEADER_CRM_ID) String crmId
    );

    @PatchMapping(value = "/apis/customers/pin-free-settings", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> updatePinFreeSettings(
            @RequestHeader(SettingExpConstant.HEADER_CRM_ID) String crmId,
            @RequestHeader(SettingExpConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestBody UpdatePinFreeSettingsRequest request
    );
}
