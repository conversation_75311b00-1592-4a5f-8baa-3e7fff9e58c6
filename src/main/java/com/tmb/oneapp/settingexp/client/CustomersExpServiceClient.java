package com.tmb.oneapp.settingexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.customers.exp.service.name}", url = "${feign.customers.exp.service.url}")
public interface CustomersExpServiceClient {

    @GetMapping(value = "/apis/customer/accounts/creditcard")
    ResponseEntity<TmbOneServiceResponse<CreditCardResponse>> getCreditCard(
            @RequestHeader HttpHeaders headers);

}