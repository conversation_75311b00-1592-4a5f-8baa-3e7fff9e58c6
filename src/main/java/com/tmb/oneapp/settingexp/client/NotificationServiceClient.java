package com.tmb.oneapp.settingexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.common.model.response.notification.NotificationResponse;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.notification.service.name}", url = "${feign.notification.service.url}")
public interface NotificationServiceClient {

    @PostMapping(value = "/apis/notification/e-noti/sendmessage", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<NotificationResponse>> sendMessage(
            @RequestHeader(SettingExpConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestBody NotificationRequest request
    );

    @PostMapping(value = "/apis/notification/e-noti/sendMessageAsync", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<NotificationResponse>> sendMessageAsync(
            @RequestHeader(SettingExpConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestBody NotificationRequest request
    );
}
