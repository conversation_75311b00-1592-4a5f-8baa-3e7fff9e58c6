package com.tmb.oneapp.settingexp.client;


import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.model.commonauthen.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.settingexp.model.commonauthen.CommonAuthenVerifyRefResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.auth.service.name}", url = "${feign.auth.service.url}")
public interface OauthFeignClient {
    @PostMapping(value = "/v1/oneapp-auth-service/oauth/common-authen/verify-ref", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CommonAuthenVerifyRefResponse>> verifyCommonAuthen(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody CommonAuthenVerifyRefRequest body);


}
