package com.tmb.oneapp.settingexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardFormatedResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.retail.lending.biz.name}", url = "${feign.retail.lending.biz.url}")
public interface RetailLendingBizClient {

    @GetMapping(value = "/v1/retail-lending-biz/creditcard")
    ResponseEntity<TmbOneServiceResponse<CreditCardFormatedResponse>> getCreditCard(
            @RequestHeader HttpHeaders headers);
}
