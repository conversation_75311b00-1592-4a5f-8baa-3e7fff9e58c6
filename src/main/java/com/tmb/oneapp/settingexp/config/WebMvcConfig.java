package com.tmb.oneapp.settingexp.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configure) {
        configure.addPathPrefix("/v1/setting-exp",  HandlerTypePredicate.forBasePackage("com.tmb.oneapp.settingexp.controller.v1"));
    }
}
