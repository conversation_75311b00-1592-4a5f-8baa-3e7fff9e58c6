package com.tmb.oneapp.settingexp.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;


@Getter
@AllArgsConstructor
public enum ResponseCode implements Serializable {
    COMMON_FR_GENERIC_ERROR("8000", "Common Face Recognition generic error"),
    SUCCESS("0000", "success"),
    FAILED("0001", "Unknown Error"),
    DATA_NOT_FOUND("0009", "Data not found"),
    VERIFY_COMMON_AUTHENTICATION_ERROR("123002", "Error verify common authentication"),

    MISSING_REQUIRED_FIELD("1000014", "Missing required field"),
    PIN_FREE_DATA_NOT_FOUND("1001001", "PIN-free data not found"),
    PIN_FREE_CONFIG_NOT_FOUND("1001002", "PIN-free configuration not found"),
    PIN_FREE_SERVICE_UNAVAILABLE("1001003", "PIN-free service unavailable");

    private final String code;
    private final String message;
    private final String service = Constants.SERVICE_NAME;
    private final String desc = null;

    private static class Constants {
        public static final String SERVICE_NAME = "setting-exp";
    }

    public static ResponseCode get(String code) {
        for (ResponseCode responseCode : ResponseCode.values()) {
            if (responseCode.getCode().equals(code)) {
                return responseCode;
            }
        }
        return null;
    }
}