package com.tmb.oneapp.settingexp.constant;

public class SettingExpConstant {
    public static final String HEADER_CORRELATION_ID = "X-Correlation-ID";
    public static final String HEADER_CRM_ID = "X-crmId";
    public static final String HEADER_ACCEPT_LANGUAGE = "Accept-Language";
    public static final String HEADER_TIMESTAMP = "Timestamp";
    public static final String HEADER_DEVICE_ID = "device-id";
    public static final String HEADER_DEVICE_MODEL = "Device-Model";
    public static final String HEADER_DEVICE_NICKNAME = "Device-Nickname";
    public static final String HEADER_APP_VERSION = "App-Version";
    public static final String HEADER_OS_VERSION = "OS-Version";
    public static final String HEADER_CHANNEL = "Channel";
    public static final String HEADER_IP_ADDRESS = "X-Forward-For";
    public static final String REFRESH_FLAG = "Refresh-flag";
    public static final String REFRESH_FLAG_VALUE_TRUE = "true";
    public static final String LIST_ALL_ACCOUNT_FLAG = "list-all-accounts-flag";
    public static final String LIST_ALL_ACCOUNT_FLAG_VALUE_TRUE = "true";
    public static final String SUCCESS = "Success";
    public static final String VALUE_MUST_NOT_BE_NULL = "Value must not be null";
    public static final String PENDING_ACTIVATION = "pending_activation";
    public static final String HEADER_CRM_ID_FOR_COMMON_AUTHEN = "crm-id";

    //View only feature
    public static final String FLOW_NAME_ENABLE_VIEW_ONLY_FOR_COMMON_AUTHEN = "Manage Account - View Only";

    // Pin-free feature
    public static final String COMMON_MODULE = "common_module";
    public static final String PIN_FREE_FEATURE_ID = "1039";
    public static final String PIN_FREE_FLOW = "Setting PIN free";
    public static final String PIN_FREE_FLAG_ENABLED = "Y";
    public static final String PIN_FREE_FLAG_DISABLED = "N";
    public static final String PIN_FREE_ACTIVITY_STATUS_ON = "ON";
    public static final String PIN_FREE_ACTIVITY_STATUS_OFF = "OFF";
    public static final String PIN_FREE_SCAN_QR_NOT_AVAILABLE = "-";

}
