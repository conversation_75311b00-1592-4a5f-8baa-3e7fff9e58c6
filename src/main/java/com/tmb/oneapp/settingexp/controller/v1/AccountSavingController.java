package com.tmb.oneapp.settingexp.controller.v1;

import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.model.AccountSaving;
import com.tmb.oneapp.settingexp.service.AccountSavingService;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/account-saving")
@Tag(name = "Account Saving Controller", description = "Proxy controller for get account saving")
public class AccountSavingController {

    private final AccountSavingService accountSavingService;

    public AccountSavingController(AccountSavingService accountSavingService) {
        this.accountSavingService = accountSavingService;
    }

    @LogAround
    @Operation(summary = "Get account saving, product group flag and mutual fund accounts")
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<AccountSaving>> getAccountsSaving(@RequestHeader HttpHeaders headers) {

        AccountSaving data = accountSavingService.getAccountSaving(headers);
        TmbServiceResponse<AccountSaving> response = new TmbServiceResponse<>();
        response.setStatus(SettingServiceUtils.getStatusSuccess());
        response.setData(data);
        return ResponseEntity.ok(response);

    }
}
