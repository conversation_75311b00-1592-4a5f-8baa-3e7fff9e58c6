package com.tmb.oneapp.settingexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.AccountSortOrderRequest;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRPostAddAccountsServiceRequest;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardFormatedResponse;
import com.tmb.oneapp.settingexp.model.manageviewonly.EnableViewOnlyRequest;
import com.tmb.oneapp.settingexp.service.ManageAccountService;
import com.tmb.oneapp.settingexp.service.ViewOnlyService;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.SUCCESS;

@RestController
@RequestMapping("/manage-account")
@Tag(name = "Manage Account", description = "Proxy controller for get manage account")
public class ManageAccountController {

    private static final TMBLogger<ManageAccountController> logger = new TMBLogger<>(ManageAccountController.class);
    private final CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;
    private final ManageAccountService manageAccountService;
    private final ViewOnlyService viewOnlyService;

    public ManageAccountController(CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient, ManageAccountService manageAccountService, ViewOnlyService viewOnlyService) {
        this.customerAccountBizServiceFeignClient = customerAccountBizServiceFeignClient;
        this.manageAccountService = manageAccountService;
        this.viewOnlyService = viewOnlyService;
    }

    @LogAround
    @Operation(summary = "Add setting account")
    @PostMapping(value = "/add")
    @Parameters({
            @Parameter(name = SettingExpConstant.HEADER_CRM_ID, description = "X-CRMID", example = "001100000000000000000001184383", in = ParameterIn.HEADER, required = true),
            @Parameter(name = SettingExpConstant.HEADER_CORRELATION_ID, description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = SettingExpConstant.HEADER_ACCEPT_LANGUAGE, description = "Accept-Language", example = "en-US, th-TH", in = ParameterIn.HEADER, required = true),
            @Parameter(name = SettingExpConstant.HEADER_TIMESTAMP, description = "Timestamp", example = "*************", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_DEVICE_ID, description = "Device-Id", example = "d28f91e4-881e-4887-a597-4a39z2822b3a", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_DEVICE_MODEL, description = "Device-Model", example = "Samsung Galaxy S20", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_DEVICE_NICKNAME, description = "Device-Nickname", example = "test nickname", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_APP_VERSION, description = "App-Version", example = "10.0", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_OS_VERSION, description = "OS-Version", example = "10.0", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_CHANNEL, description = "Channel", example = "mb", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_IP_ADDRESS, description = "IP-Address", example = "127.0.0.1", in = ParameterIn.HEADER)
    })
    public ResponseEntity<TmbServiceResponse<Void>> postAccountsSavingAddCommonFR(
            @RequestHeader HttpHeaders headers,
            @RequestBody @Valid CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest
    ) throws TMBCommonException {
        manageAccountService.postAccountsSavingAddCommonFR(SettingServiceUtils.initialOneAppHeader(headers), commonFRPostAddAccountsServiceRequest);
        TmbServiceResponse<Void> response = new TmbServiceResponse<>();
        response.setStatus(SettingServiceUtils.getStatusSuccess());

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Get re-order account by personal sort, account saving, product group flag and mutual fund accounts")
    @PutMapping(value = "/reorder", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> updatePersonalizedAccountSortOrder(@Parameter(name = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
                                                                                         @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000000086006", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId,
                                                                                         @RequestBody List<AccountSortOrderRequest> request,
                                                                                         @RequestHeader HttpHeaders headers) {
        customerAccountBizServiceFeignClient.updateReorderAccount(request, SettingServiceUtils.initialOneAppHeader(headers));
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(SettingServiceUtils.getStatusSuccess());
        response.setData(SUCCESS);

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Get manage credit cards")
    @GetMapping(value = "/credit-card", produces = MediaType.APPLICATION_JSON_VALUE)
    @Parameters({
            @Parameter(name = SettingExpConstant.HEADER_CRM_ID, description = "X-CRMID", example = "001100000000000000000001184383", in = ParameterIn.HEADER, required = true),
            @Parameter(name = SettingExpConstant.HEADER_CORRELATION_ID, description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true)
    })
    public ResponseEntity<TmbServiceResponse<CreditCardFormatedResponse>> manageCreditCard(@RequestHeader HttpHeaders headers) throws TMBCommonException {

        TmbServiceResponse<CreditCardFormatedResponse> response = new TmbServiceResponse<>();
        response.setStatus(SettingServiceUtils.getStatusSuccess());
        response.setData(manageAccountService.getCreditCardList(headers));

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Set The account for view only (Cannot do transaction)")
    @PutMapping(value = "/enable-view-only")
    @Parameters({
            @Parameter(name = SettingExpConstant.HEADER_CRM_ID, description = "X-CRMID", example = "001100000000000000000001184383", in = ParameterIn.HEADER, required = true),
            @Parameter(name = SettingExpConstant.HEADER_CORRELATION_ID, description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = SettingExpConstant.HEADER_ACCEPT_LANGUAGE, description = "Accept-Language", example = "en-US, th-TH", in = ParameterIn.HEADER, required = true),
            @Parameter(name = SettingExpConstant.HEADER_TIMESTAMP, description = "Timestamp", example = "*************", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_DEVICE_ID, description = "Device-Id", example = "d28f91e4-881e-4887-a597-4a39z2822b3a", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_DEVICE_MODEL, description = "Device-Model", example = "Samsung Galaxy S20", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_DEVICE_NICKNAME, description = "Device-Nickname", example = "test nickname", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_APP_VERSION, description = "App-Version", example = "10.0", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_OS_VERSION, description = "OS-Version", example = "10.0", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_CHANNEL, description = "Channel", example = "mb", in = ParameterIn.HEADER),
            @Parameter(name = SettingExpConstant.HEADER_IP_ADDRESS, description = "IP-Address", example = "127.0.0.1", in = ParameterIn.HEADER)
    })
    public ResponseEntity<TmbServiceResponse<String>> enableViewOnly(
            @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Valid @RequestHeader(HEADER_CRM_ID) @RequestHeaderNonNull String crmId,
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody EnableViewOnlyRequest request
    ) throws TMBCommonException {
        viewOnlyService.enableViewOnly(SettingServiceUtils.initialOneAppHeader(headers), request);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(SettingServiceUtils.getStatusSuccess());
        response.setData("success");

        return ResponseEntity.ok(response);
    }
}
