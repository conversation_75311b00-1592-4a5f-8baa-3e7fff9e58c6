package com.tmb.oneapp.settingexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.settingexp.model.pinfree.PinFreeResponse;
import com.tmb.oneapp.settingexp.model.pinfree.UpdatePinFreeSettingsRequest;
import com.tmb.oneapp.settingexp.service.PinFreeService;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CRM_ID;

@RestController
@RequestMapping("/pin-free")
@Tag(name = "PIN Free", description = "Controller for PIN free configuration")
public class PinFreeController {

    private static final TMBLogger<PinFreeController> logger = new TMBLogger<>(PinFreeController.class);

    private final PinFreeService pinFreeService;

    public PinFreeController(PinFreeService pinFreeService) {
        this.pinFreeService = pinFreeService;
    }

    @LogAround
    @Operation(summary = "Get PIN free configuration")
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<PinFreeResponse>> getPinFreeConfig(
            @Parameter(name = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000025532221", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId
    ) throws TMBCommonException {
        logger.info("Getting PIN-free configuration for correlationId: {}, crmId: {}", correlationId, crmId);

        // Get PIN-free data from service
        PinFreeResponse pinFreeData = pinFreeService.getPinFreeData(correlationId, crmId);

        // Create response wrapper
        TmbServiceResponse<PinFreeResponse> response = new TmbServiceResponse<>();
        response.setStatus(SettingServiceUtils.getStatusSuccess());
        response.setData(pinFreeData);

        logger.info("Successfully retrieved PIN-free configuration for crmId: {}", crmId);
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Update PIN free settings")
    @PostMapping(value = "/settings", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> updatePinFreeSettings(
            @Parameter(name = HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000025532221", required = true) @Valid @RequestHeader(HEADER_CRM_ID) @RequestHeaderNonNull String crmId,
            @Valid @RequestBody UpdatePinFreeSettingsRequest request
    ) throws TMBCommonException {
        logger.info("Updating PIN-free configuration for correlationId: {}, crmId: {}", correlationId, crmId);

        String updateResult = pinFreeService.updatePinFreeSettings(correlationId, crmId, request);

        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(SettingServiceUtils.getStatusSuccess());
        response.setData(updateResult);

        logger.info("Successfully updated PIN-free configuration for crmId: {}", crmId);
        return ResponseEntity.ok(response);
    }
}
