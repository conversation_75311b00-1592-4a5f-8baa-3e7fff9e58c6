package com.tmb.oneapp.settingexp.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountSaving {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalAvailableBalance;
    private boolean cacheFlag;

    private List<DepositAccount> depositAccountLists;
    private List<DepositAccount> fcdAccountLists;
    private List<ProductGroupFlag> productGroupFlag;
    private List<String> mutualFundAccounts;
    private List<LoanAccount> loanAccounts;
    private List<LoanAccount> hpAccounts;
}
