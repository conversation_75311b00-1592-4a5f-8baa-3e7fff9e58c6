package com.tmb.oneapp.settingexp.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AccountSortOrderRequest {
    @NotNull(message = "Key cannot be null")
    @NotEmpty(message = "Key cannot be empty string")
    @NotBlank(message = "Key cannot be blank")
    private String accountNumber;

    @NotNull(message = "Key cannot be null")
    @NotEmpty(message = "Key cannot be empty string")
    @NotBlank(message = "Key cannot be blank")
    private Integer accountSortOrder;
}
