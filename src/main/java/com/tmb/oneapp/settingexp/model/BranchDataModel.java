package com.tmb.oneapp.settingexp.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@ToString
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BranchDataModel {
    @Schema(description = "Branch ID")
    @JsonProperty("branch_id")
    private String branchId;

    @Schema(description = "Branch code")
    @JsonProperty("branch_code")
    private String branchCode;

    @Schema(description = "Branch name Thai")
    @JsonProperty("branch_name_th")
    private String branchNameTh;

    @Schema(description = "Branch name English")
    @JsonProperty("branch_name_en")
    private String branchNameEn;

    @Schema(description = "Branch working time in Thai")
    @JsonProperty("branch_working_time_th")
    private String branchWorkingTimeTh;

    @Schema(description = "Branch working time in English")
    @JsonProperty("branch_working_time_en")
    private String branchWorkingTimeEn;

    @Schema(description = "Branch address Thai")
    @JsonProperty("br_address_th")
    private String brAddressTh;

    @Schema(description = "Branch address English")
    @JsonProperty("br_address_en")
    private String brAddressEn;


    @Schema(description = "Branch latitude")
    @JsonProperty("br_latitude")
    private String brLatitude;

    @Schema(description = "Branch longitude")
    @JsonProperty("br_longitude")
    private String brLongitude;

    @Schema(description = "Branch primary phone number")
    @JsonProperty("primary_phone_number")
    private String primaryPhoneNumber;

    @Schema(description = "Branch other phone number")
    @JsonProperty("other_phone_number")
    private String otherPhoneNumber;

    @Schema(description = "Branch province code")
    @JsonProperty("province_cd")
    private String provinceCd;

    @Schema(description = "Branch province name in Thai")
    @JsonProperty("province_name_th")
    private String provinceNameTh;

    @Schema(description = "Branch province name in English")
    @JsonProperty("province_name_en")
    private String provinceNameEn;
}