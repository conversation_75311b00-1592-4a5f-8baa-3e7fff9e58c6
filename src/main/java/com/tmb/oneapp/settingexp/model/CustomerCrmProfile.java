package com.tmb.oneapp.settingexp.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerCrmProfile {
    private String crmId;
    private Integer ebTxnLimitAmt;
    private Integer ebMaxLimitAmtCurrent;
    private String ebMaxLimitTempFlag;
    private Integer ebMaxLimitAmtHist;
    private Integer ebMaxLimitAmtRequest;
    private Double ebAccuUsgAmtDaily;
    private String ebCustomerStatusId;
    private String ebUserStatusDesc;
    private String ibUserStatusId;
    private String mbUserStatusId;
    private String mbUserStatusDesc;
    private String referCd;
    private Integer ottMaxLimitAmtCurrent;
    private String ottMaxLimitTempFlag;
    private Integer ottAccuUsgAmtDaily;
    private Integer ottMaxLimitAmtHist;
    private Integer ottMaxLimitAmtRequest;
    private String pinFreeSeetingFlag;
    private Integer pinFreeTrLimit;
    private Integer pinFreeBpLimit;
    private Integer pinFreeTxnCount;
    private BigDecimal pinFreeQrLimit;
    private BigDecimal pinFreeAccuUsgAmt;
    private String defaultAcctId;
    private String autoSaveSlipMain;
    private String autoSaveSlipOwn;
    private String quickBalanceSettingFlag;
    private Integer cardlessMaxLimitAmt;
    private Integer cardlessAccuUsgAmt;
    private Integer cardlessMaxLimitAmtHist;
    private String cardlessMaxLimitTempFlag;
    private String maskAcctIdFlag;
    private String faceRecognizeMode;
    private String frAllowFlag;
    private Integer billpayMaxLimitAmtHist;
    private String billpayMaxLimitTempFlag;
    private Integer billpayMaxLimitAmt;
    private BigDecimal billpayAccuUsgAmt;
    private BigDecimal paymentAccuUsgAmt;
    private CustomerProfilingData customerProfilingData;
}