package com.tmb.oneapp.settingexp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerProfilingData {

    private String customerGroupLimit;
    private String customerGroupLimitDisplayEn;
    private String customerGroupLimitDisplayTh;
    private Integer domesticTransMaxLimit;
    private Integer internationalTransMaxLimit;
    private Integer billpayTransMaxLimit;
    private Integer cardlessTransMaxLimit;

    private String customerGroupLimitEligible;
    private Integer domesticTransMaxLimitEligible;
    private Integer internationalTransMaxLimitEligible;
    private Integer billpayTransMaxLimitEligible;
    private Integer cardlessTransMaxLimitEligible;

    private boolean isMinimumOsVersionPassed;
    private String customerGroupUpdateLatest;
    @JsonProperty("y_age")
    private String yAge;
    @JsonProperty("y_max_limit")
    private Integer yMaxLimit;
    @JsonProperty("r_max_limit")
    private Integer rMaxLimit;
    private List<GroupLimit> groupLimitList;

    // ----- ONEAPP-949555 NEW FIELDS ADDED -----
    private Boolean warningLabelIsDisplay;
    private String warningLabelEnHeaderDescription;
    private String warningLabelThHeaderDescription;
    private String warningLabelEnDetailDescription;
    private String warningLabelThDetailDescription;
    private String warningIcon;
    // ---------------------------------

    @Data
    @ToString
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class GroupLimit {
        private String groupId;
        private Integer domesticMaxLimit;
        private Integer internationalMaxLimit;
        private Integer billpayMaxLimit;
        private Integer cardlessMaxLimit;
        private Integer displayOrder;
        private String displayGroupTh;
        private String displayGroupEn;
        private List<com.tmb.common.model.CustomerProfilingGroupLimitConfig.Description> descriptionTh;
        private List<com.tmb.common.model.CustomerProfilingGroupLimitConfig.Description> descriptionEn;
        private String eligibleMessageId;
        private String groupMessageId;
    }

}