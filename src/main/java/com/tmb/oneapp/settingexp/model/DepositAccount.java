package com.tmb.oneapp.settingexp.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DepositAccount {
    private String productCode;
    private String productNickname;
    private String productNameTh;
    private String productNameEn;
    private String accountNumber;
    private String accountStatus;
    private String displayAccountStatus;
    private String rmAccountStatus;
    private String accountType;
    private String accountTypeDescEn;
    private String accountTypeDescTh;
    private String accountName;
    private String branchCode;
    private String branchNameEn;
    private String branchNameTh;
    private String relationshipCode;
    private String allowFromForBillPayTopUpEpayment;
    private String allowTransferFromAccount;
    private String allowSetQuickBalance;
    private String allowCardLessWithdraw;
    private String allowPayLoanDirectDebit;
    private String allowReceiveLoanFund;
    private String waiveFeeForBillpay;
    private String transferOwnTTBMapCode;
    private String transferOtherTTBMapCode;
    private String allowTransferToLinkedAccount;
    private String linkedAccount;
    private String allowTransferToOtherBank;
    private String allowTransferToPromptPay;
    private String waiveFeeForPromptPayAccount;
    private String waiveFeeForPromptPay;
    private String ledgerBalance;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal availableBalance;

    private String productIconUrl;
    private String productConfigSortOrder;
    private boolean hideAccountFlag;
    private boolean switchAllFreeFlag;

    //add for support beneficial detail
    private String acctCtl1;
    private String acctCtl2;
    private String acctCtl3;
    private String acctCtl4;

    private String openOnlineAllowFrom;
    private String openOnlineFirstDepositMin;
    private String openOnlineFirstDepositMax;

    private String allowDirectDebit;
    private String allowManageDebitCard;

    private String accountDetailView;
    private String allowRegisterPromptPay;

    private String allowToDstatementDirectdebitFee;
    private String allowToRequestDstatement;
    private String chequeStop;
    private String balanceCurrency;
    private String financialId;
    private String allowFcdFee;
    private String allowFcdExchange;
    private Boolean fcdExchangeFlag;
    private String openingMethod;
    private String xpsAccountStatus;
    private Boolean isPaymentAccount;
    private String allowToRequestFinCert;
    private String allowToFinCertDirectdebitFee;
    private String directDebitShortcutFlag;
    private String allowToApplySecuredCard;
    private Integer personalizedAcctSortOrder;
    private boolean viewOnlyFlag;
}

