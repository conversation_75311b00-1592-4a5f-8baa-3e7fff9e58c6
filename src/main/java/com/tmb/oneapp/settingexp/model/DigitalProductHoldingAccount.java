package com.tmb.oneapp.settingexp.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DigitalProductHoldingAccount {

    private String applCode;
    private String acctCtrl1;
    private String acctCtrl2;
    private String acctCtrl3;
    private String acctCtrl4;
    private String acctNbr;
    private String accountName;
    private String productGroupCode;
    private String productGroupCodeEc;
    private String productCode;
    private String ownerType;
    private String relationshipCode;
    private String accountOpenDt;
    private String accountStatus;
    private Double currentBalance;
    private String balanceCurrency;
    private String rmAccountStatus;
    private String xpsAccountStatus;
    private String ledgerBalance;
    private String openingMethod;
}
