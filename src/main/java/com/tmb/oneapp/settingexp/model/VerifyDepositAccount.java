package com.tmb.oneapp.settingexp.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VerifyDepositAccount {
    String crmId;
    String accountNo;
    String accountType;
    String branch;
    String verifyAccountRef;
    String displayAccountStatus;
}
