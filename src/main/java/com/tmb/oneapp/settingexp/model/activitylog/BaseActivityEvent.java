package com.tmb.oneapp.settingexp.model.activitylog;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.BaseEvent;
import org.springframework.http.HttpHeaders;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CHANNEL;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_DEVICE_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_DEVICE_MODEL;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_IP_ADDRESS;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_OS_VERSION;

public abstract class BaseActivityEvent extends BaseEvent {
    private static final TMBLogger<BaseActivityEvent> logger = new TMBLogger<>(BaseActivityEvent.class);
    public static String ACTIVITY_STATUS_FAILURE = "Failure";
    public static String ACTIVITY_STATUS_SUCCESS_LOWER_CASE = "success";

    protected BaseActivityEvent(HttpHeaders headers) {
        if (headers == null) {
            return;
        }
        logger.info("Header for save act log :{}", headers);
        super.setActivityDate(Long.toString(System.currentTimeMillis()));
        super.setIpAddress(headers.getFirst(HEADER_IP_ADDRESS));
        super.setOsVersion(headers.getFirst(HEADER_OS_VERSION));
        super.setChannel(headers.getFirst(HEADER_CHANNEL));
        super.setAppVersion(headers.getFirst(HEADER_APP_VERSION));
        super.setDeviceId(headers.getFirst(HEADER_DEVICE_ID));
        super.setDeviceModel(headers.getFirst(HEADER_DEVICE_MODEL));
        super.setCrmId(headers.getFirst(HEADER_CRM_ID));
        super.setCorrelationId(headers.getFirst(HEADER_CORRELATION_ID));
        super.setActivityStatus(ACTIVITY_STATUS_SUCCESS_LOWER_CASE);
    }
}
