package com.tmb.oneapp.settingexp.model.activitylog;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PinFreeActivityEvent extends BaseActivityEvent {
    public static String PIN_FREE_ACTIVITY_ID = "100200604";
    public static String PIN_FREE_FLOW = "Setting";

    private String flow;
    private String pinFreeFlag;
    private String scanQr;

    public PinFreeActivityEvent(HttpHeaders headers) {
        super(headers);
        setActivityTypeId(PIN_FREE_ACTIVITY_ID);
        setFlow(PIN_FREE_FLOW);
    }
}
