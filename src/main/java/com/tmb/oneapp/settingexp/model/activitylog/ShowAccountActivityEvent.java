package com.tmb.oneapp.settingexp.model.activitylog;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ShowAccountActivityEvent extends BaseActivityEvent {
    public static String SHOW_ACCOUNT_ACTIVITY_ID = "*********";
    public static String SHOW_ACCOUNT_ADD = "Add";

    @JsonProperty("set_show_account")
    private String showAccount;
    private String accountNo;
    private String accountNickname;
    private String accountType;
    private String branch;
    public ShowAccountActivityEvent(HttpHeaders headers) {
        super(headers);
        setActivityTypeId(SHOW_ACCOUNT_ACTIVITY_ID);
    }
}
