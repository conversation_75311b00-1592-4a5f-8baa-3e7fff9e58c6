package com.tmb.oneapp.settingexp.model.commonauthen;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonAuthenVerifyRefResponse {
    private Boolean result;
    private Boolean frRequire;
    private String frStatusCode;
    private String destination;
    private String bankCode;
    private String billerCode;
    private String productCode;
    private String amount;
    private String dailyAmount;
    private String featureId;
}
