package com.tmb.oneapp.settingexp.model.commonfr;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRPostAddAccountsServiceRequest {
    @NotBlank(message = "uuid must not be null/blank")
    private String uuid;
    @NotBlank(message = "verify_account_ref_id must not be null/blank")
    private String verifyAccountRefId;
    @NotBlank(message = "account_nickname must not be null/blank")
    private String accountNickname;
    @NotNull(message = "feature_id must not be null")
    private Integer featureId;
    @NotBlank(message = "flow must not be null/blank")
    private String flow;
}
