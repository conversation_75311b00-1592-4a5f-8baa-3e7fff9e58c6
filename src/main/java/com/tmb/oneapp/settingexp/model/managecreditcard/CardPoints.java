package com.tmb.oneapp.settingexp.model.managecreditcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardPoints {
    private String pointEarned;
    private String pointUsed;
    private String pointAvailable;
    private String pointRemain;
    private String expiryDate;
    private String expiryPoints;
}
