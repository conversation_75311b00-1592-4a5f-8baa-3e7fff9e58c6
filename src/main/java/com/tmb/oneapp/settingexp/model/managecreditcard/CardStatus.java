package com.tmb.oneapp.settingexp.model.managecreditcard;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CardStatus {
    private Integer stopCode;
    private String stopCodeDesc;
    private String accountStatus;
    private String accountAgreeStatus;
    private String activatedDate;
    private String blockCode;
    private String previousExpiryDate;
    private String applicationType;
    private String stopDate;
    private String cardPloanFlag;
    private String cardActiveFlag;
    private String cardRole;
    private String virtualCardFlag;
}