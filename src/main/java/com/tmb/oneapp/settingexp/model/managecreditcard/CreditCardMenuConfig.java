package com.tmb.oneapp.settingexp.model.managecreditcard;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreditCardMenuConfig {

    private String productId;
    private String cardType;
    private String virtualFlag;
    private String productGroup;
    private String displayRewardPoint;
    private List<Menu> menuMainFeatures;
    private List<Menu> menuManageCard;
    private List<Menu> menuManageCardDetail;

}
