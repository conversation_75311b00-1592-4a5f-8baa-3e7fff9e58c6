package com.tmb.oneapp.settingexp.model.manageviewonly;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EnableViewOnlyRequest extends MangeViewOnlyRequest {
    @NotBlank(message = "fr_uuid cannot be blank")
    private String frUuid;
}
