package com.tmb.oneapp.settingexp.model.pinfree;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonAuthenticationInformation {
    private String featureId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#0")
    private BigDecimal amount;

    private String flow;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#0")
    private BigDecimal dailyAmount;
}