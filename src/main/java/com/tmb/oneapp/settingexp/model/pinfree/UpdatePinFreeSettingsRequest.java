package com.tmb.oneapp.settingexp.model.pinfree;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdatePinFreeSettingsRequest {

    @Schema(description = "Pin free setting flag", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Pin free setting flag is required")
    private Boolean pinFreeEnableFlag;

    @Schema(description = "Pin free QR limit", example = "5000.00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DecimalMin(value = "1.0", inclusive = false, message = "Pin free QR limit must be greater than 1.0")
    private BigDecimal pinFreeQrLimit;

    public UpdatePinFreeSettingsRequest() {
    }

    public UpdatePinFreeSettingsRequest(Boolean pinFreeEnableFlag, BigDecimal pinFreeQrLimit) {
        this.pinFreeEnableFlag = pinFreeEnableFlag;
        this.pinFreeQrLimit = pinFreeQrLimit;
    }

    @JsonSetter("pin_free_enable_flag")
    public void setPinFreeEnableFlag(Boolean pinFreeEnableFlag) {
        this.pinFreeEnableFlag = pinFreeEnableFlag;
    }

    @JsonGetter("pin_free_enable_flag")
    public String getPinFreeEnableFlagAsYn() {
        return Boolean.TRUE.equals(pinFreeEnableFlag)
                ? SettingExpConstant.PIN_FREE_FLAG_ENABLED
                : SettingExpConstant.PIN_FREE_FLAG_DISABLED;
    }

    @JsonIgnore
    public Boolean getPinFreeEnableFlag() {
        return pinFreeEnableFlag;
    }

    public BigDecimal getPinFreeQrLimit() {
        return pinFreeQrLimit;
    }

    public void setPinFreeQrLimit(BigDecimal pinFreeQrLimit) {
        this.pinFreeQrLimit = pinFreeQrLimit;
    }
}
