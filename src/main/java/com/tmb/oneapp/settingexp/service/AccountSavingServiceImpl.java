package com.tmb.oneapp.settingexp.service;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.client.BankServiceClient;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.AccountSaving;
import com.tmb.oneapp.settingexp.model.BranchDataModel;
import com.tmb.oneapp.settingexp.model.DepositAccount;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.LIST_ALL_ACCOUNT_FLAG_VALUE_TRUE;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.REFRESH_FLAG_VALUE_TRUE;

@Service
public class AccountSavingServiceImpl implements AccountSavingService {
    private static final TMBLogger<AccountSavingServiceImpl> logger = new TMBLogger<>(AccountSavingServiceImpl.class);
    private final CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;
    private final BankServiceClient bankServiceClient;

    public AccountSavingServiceImpl(CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient, BankServiceClient bankServiceClient) {
        this.customerAccountBizServiceFeignClient = customerAccountBizServiceFeignClient;
        this.bankServiceClient = bankServiceClient;
    }

    @Override
    public AccountSaving getAccountSaving(HttpHeaders headers) {
        String correlationId = headers.getFirst(SettingExpConstant.HEADER_CORRELATION_ID);

        AccountSaving accountsSaving = getAccountsSaving(headers);

        Map<String, BranchDataModel> branchDataModelMap = getBranchData(correlationId);

        accountsSaving.getDepositAccountLists().forEach(a -> setBranchName(a, branchDataModelMap));
        accountsSaving.getFcdAccountLists().forEach(a -> setBranchName(a, branchDataModelMap));
        return accountsSaving;
    }

    private AccountSaving getAccountsSaving(HttpHeaders headers) {
        try {
            return Optional.of(customerAccountBizServiceFeignClient.getAccountsSaving(SettingServiceUtils.initialOneAppHeader(headers), LIST_ALL_ACCOUNT_FLAG_VALUE_TRUE, null))
                    .map(ResponseEntity::getBody)
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();

        } catch (Exception e) {
            logger.error("Error customerAccountBizServiceFeignClient.getAccountsSaving", e);
            throw e;
        }
    }

    private Map<String, BranchDataModel> getBranchData(String correlationId) {
        try {
            List<BranchDataModel> branchDataModelList = Optional.of(bankServiceClient.getBranch(correlationId))
                    .map(ResponseEntity::getBody)
                    .map(TmbOneServiceResponse::getData)
                    .orElseThrow();

            return branchDataModelList.stream()
                    .collect(Collectors.toMap(BranchDataModel::getBranchCode, Function.identity()));
        } catch (Exception e) {
            logger.error("Error bankServiceClient.getBranch", e);
            throw e;
        }
    }

    private void setBranchName(DepositAccount a, Map<String, BranchDataModel> branchDataModelMap) {
        BranchDataModel branchData = branchDataModelMap.get(a.getBranchCode());
        if (branchData != null) {
            a.setBranchNameEn(branchData.getBranchNameEn());
            a.setBranchNameTh(branchData.getBranchNameTh());
        }
    }
}
