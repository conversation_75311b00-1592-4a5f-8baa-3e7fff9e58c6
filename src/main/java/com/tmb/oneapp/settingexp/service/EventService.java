package com.tmb.oneapp.settingexp.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.BaseEvent;
import com.tmb.common.util.TMBUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class EventService {
    private static final TMBLogger<EventService> logger = new TMBLogger<>(EventService.class);
    private final KafkaProducerService kafkaProducerService;
    private final String topicName;

    public EventService(
            KafkaProducerService kafkaProducerService,
            @Value("${setting.exp.service.activity.topic.name}") String topicName
    ) {
        this.kafkaProducerService = kafkaProducerService;
        this.topicName = topicName;
    }
    public boolean publish(BaseEvent event) {
        try {
            String message = TMBUtils.convertJavaObjectToString(event);
            kafkaProducerService.sendMessageAsync(topicName, message);
            logger.info("Capture activity log id: {} result: {}", event.getActivityTypeId(), event.getActivityStatus());
            logger.info("Publish message success for message: {}", message);
            return true;
        } catch (Exception e) {
            logger.error("Publish message failed for activity log id: {}", event.getActivityTypeId(), e);
            return false;
        }
    }
}
