package com.tmb.oneapp.settingexp.service;


import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRPostAddAccountsServiceRequest;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardFormatedResponse;
import org.springframework.http.HttpHeaders;

public interface ManageAccountService {
    void postAccountsSavingAddCommonFR(HttpHeaders headers, CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest) throws TMBCommonException;

    CreditCardFormatedResponse getCreditCardList(HttpHeaders headers) throws TMBCommonException;
}
