package com.tmb.oneapp.settingexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.service.RedisUtilService;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.client.CustomerServiceClient;
import com.tmb.oneapp.settingexp.client.RetailLendingBizClient;
import com.tmb.oneapp.settingexp.constant.DisplayAccountStatusConstants;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.AddDepositAccountRequest;
import com.tmb.oneapp.settingexp.model.VerifyDepositAccount;
import com.tmb.oneapp.settingexp.model.activitylog.BaseActivityEvent;
import com.tmb.oneapp.settingexp.model.activitylog.ShowAccountActivityEvent;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRPostAddAccountsServiceRequest;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRVerifyFRRequest;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRVerifyFRResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardFormatedResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardSupplementary;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.tmb.oneapp.settingexp.constant.DisplayAccountStatusConstants.VERIFY_ACCOUNT_PREFIX_CACHE_KEY;
import static com.tmb.oneapp.settingexp.model.activitylog.ShowAccountActivityEvent.SHOW_ACCOUNT_ADD;

@Service
public class ManageAccountServiceImpl implements ManageAccountService {
    private static final TMBLogger<ManageAccountServiceImpl> logger = new TMBLogger<>(ManageAccountServiceImpl.class);
    private final CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;
    private final CustomerServiceClient customerServiceClient;
    private final RetailLendingBizClient retailLendingBizClient;
    private final RedisUtilService redisUtilService;
    private final EventService eventService;

    public ManageAccountServiceImpl(CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient, CustomerServiceClient customerServiceClient, RetailLendingBizClient retailLendingBizClient, RedisUtilService redisUtilService, EventService eventService) {
        this.customerAccountBizServiceFeignClient = customerAccountBizServiceFeignClient;
        this.customerServiceClient = customerServiceClient;
        this.retailLendingBizClient = retailLendingBizClient;
        this.redisUtilService = redisUtilService;
        this.eventService = eventService;
    }

    @Override
    public void postAccountsSavingAddCommonFR(HttpHeaders headers, CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest) throws TMBCommonException {
        String crmId = headers.getFirst(SettingExpConstant.HEADER_CRM_ID);
        String correlationId = headers.getFirst(SettingExpConstant.HEADER_CORRELATION_ID);
        String acceptLanguage = headers.getFirst(SettingExpConstant.HEADER_ACCEPT_LANGUAGE);
        String appVersion = headers.getFirst(SettingExpConstant.HEADER_APP_VERSION);
        String ipAddress = headers.getFirst(SettingExpConstant.HEADER_IP_ADDRESS);

        String requestUuid = commonFRPostAddAccountsServiceRequest.getUuid();
        String requestVerifyAccountRefId = commonFRPostAddAccountsServiceRequest.getVerifyAccountRefId();
        String requestAccountNickname = commonFRPostAddAccountsServiceRequest.getAccountNickname();
        Integer requestFeatureId = commonFRPostAddAccountsServiceRequest.getFeatureId();
        String requestFlow = commonFRPostAddAccountsServiceRequest.getFlow();

        String uuid = this.postCommonFRVerifyFR(crmId, correlationId, acceptLanguage, appVersion, ipAddress, new CommonFRVerifyFRRequest().setUuid(requestUuid).setFeatureId(requestFeatureId).setFlow(requestFlow)).getData().getUuid();
        if (StringUtils.isBlank(uuid)) {
            logger.error("UUID not found in Redis");
            writeFailedActivityLog(headers, commonFRPostAddAccountsServiceRequest, requestVerifyAccountRefId);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.COMMON_FR_GENERIC_ERROR, HttpStatus.BAD_REQUEST);
        }

        addDepositAccount(headers, requestVerifyAccountRefId, requestAccountNickname);
    }

    @Override
    public CreditCardFormatedResponse getCreditCardList(HttpHeaders headers) throws TMBCommonException {
        String crmId = headers.getFirst(SettingExpConstant.HEADER_CRM_ID);
        String correlationId = headers.getFirst(SettingExpConstant.HEADER_CORRELATION_ID);
        if (StringUtils.isEmpty(crmId) || StringUtils.isEmpty(correlationId)) {
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, SettingExpConstant.VALUE_MUST_NOT_BE_NULL, HttpStatus.BAD_REQUEST);
        }
        logger.info("getCreditCardList by crmId {}", crmId);

        CreditCardFormatedResponse creditCardResponse = retailLendingBizClient.getCreditCard(headers).getBody().getData();
        List<CreditCardSupplementary> creditCard = mapDisplayExpireDate(creditCardResponse.getCreditCards());
        List<CreditCardSupplementary> flashCard = mapDisplayExpireDate(creditCardResponse.getFlashCards());

        List<CreditCardSupplementary> filterCreditCard = this.filterPendingActivationCard(creditCard);
        List<CreditCardSupplementary> filterFlashCard = this.filterPendingActivationCard(flashCard);
        if (CollectionUtils.isEmpty(filterCreditCard) && CollectionUtils.isEmpty(filterFlashCard)) {
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.DATA_NOT_FOUND, HttpStatus.OK);
        }
        return CreditCardFormatedResponse.builder()
                .creditCards(filterCreditCard)
                .flashCards(filterFlashCard)
                .build();
    }

    private void addDepositAccount(HttpHeaders headers, String requestVerifyAccountRefId, String requestAccountNickname) throws TMBCommonException {
        try {
            AddDepositAccountRequest request = new AddDepositAccountRequest();
            request.setVerifyAccountRefId(requestVerifyAccountRefId);
            request.setAccountNickname(requestAccountNickname);
            customerAccountBizServiceFeignClient.addDepositAccount(request, headers);
        } catch (Exception ex) {
            logger.error("Error customerAccountBizServiceFeignClient.addDepositAccount", ex);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.COMMON_FR_GENERIC_ERROR, HttpStatus.BAD_REQUEST);
        }
    }

    private void writeFailedActivityLog(HttpHeaders headers, CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest, String requestVerifyAccountRefId) throws TMBCommonException {
        VerifyDepositAccount verifyDepositAccount = this.getVerifyDepositAccountFromCache(VERIFY_ACCOUNT_PREFIX_CACHE_KEY.concat(requestVerifyAccountRefId));

        ShowAccountActivityEvent sEvent = new ShowAccountActivityEvent(headers);
        sEvent.setShowAccount(SHOW_ACCOUNT_ADD);
        sEvent.setAccountNo(verifyDepositAccount.getAccountNo());
        sEvent.setAccountType(verifyDepositAccount.getAccountType());
        sEvent.setBranch(verifyDepositAccount.getBranch());
        sEvent.setAccountNickname(commonFRPostAddAccountsServiceRequest.getAccountNickname());
        sEvent.setActivityStatus(BaseActivityEvent.ACTIVITY_STATUS_FAILURE);
        sEvent.setFailReason("Verify Current FR incorrect.");
        eventService.publish(sEvent);
    }

    public TmbServiceResponse<CommonFRVerifyFRResponse> postCommonFRVerifyFR(String crmId, String correlationId, String acceptLanguage, String appVersion, String ipAddress, CommonFRVerifyFRRequest request) throws TMBCommonException {
        try {
            logger.info("calling postCommonFRVerifyFR");
            return Objects.requireNonNull(customerServiceClient.postCommonFRVerifyFR(
                    crmId,
                    correlationId,
                    acceptLanguage,
                    appVersion,
                    ipAddress,
                    request
            ).getBody());
        } catch (Exception ex) {
            logger.error("Error when call POST /v1/customers-service/common-fr/verify-fr : {}", ex);
            throw new TMBCommonException(ResponseCode.COMMON_FR_GENERIC_ERROR.getCode(),
                    ResponseCode.COMMON_FR_GENERIC_ERROR.getMessage(),
                    ResponseCode.COMMON_FR_GENERIC_ERROR.getService(), HttpStatus.BAD_REQUEST, ex
            );
        }
    }

    public VerifyDepositAccount getVerifyDepositAccountFromCache(String key) throws TMBCommonException {
        try {
            String accountDetailCacheValue = redisUtilService.get(key);
            if (StringUtils.isBlank(accountDetailCacheValue)) {
                logger.error("Error Invalid request should verify account first, Account from cache is null. [keyCache = {}]", key);
                throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED,
                        DisplayAccountStatusConstants.ERROR_ADD_DEPOSIT_ACCOUNT_WITHOUT_VERIFY_DES,
                        HttpStatus.BAD_REQUEST);
            }
            return (VerifyDepositAccount) TMBUtils.convertStringToJavaObj(accountDetailCacheValue, VerifyDepositAccount.class);
        } catch (JsonProcessingException e) {
            logger.error("Error Cannot parse json from cache. [keyCache = {}]", key);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, "Cannot parse json from cache");
        }
    }

    private List<CreditCardSupplementary> mapDisplayExpireDate(List<CreditCardSupplementary> cards) {
        if (CollectionUtils.isEmpty(cards)) {
            return Collections.emptyList();
        }
        cards.forEach(card -> card.setDisplayExpireDate(getDisplayExpireDate(cards, card)));
        return cards;
    }

    private String getDisplayExpireDate(List<CreditCardSupplementary> cards, CreditCardSupplementary card) {
        if (isRenewalCardExist(cards, card.getAccountId())) {
            String previousExpireDate = this.reorderDate(card.getCardStatus().getPreviousExpiryDate());
            String expireDate = this.reorderDate(card.getExpiredBy());
            return previousExpireDate.compareTo(expireDate) < 0 ? previousExpireDate : expireDate;
        }

        return this.reorderDate(card.getExpiredBy());
    }

    private boolean isRenewalCardExist(List<CreditCardSupplementary> cards, String accountId) {
        return cards.stream().anyMatch(card ->
                null != card.getAccountId() &&
                        card.getAccountId().equals(accountId) &&
                        card.getAccountStatus().equals("pending_activation") &&
                        !card.getCardStatus().getPreviousExpiryDate().equals("0000"));
    }

    private String reorderDate(String date) {
        return !StringUtils.isEmpty(date) && date.length() == 4 ? date.substring(2, 4) + "/" + date.substring(0, 2) : date;
    }

    public List<CreditCardSupplementary> filterPendingActivationCard(List<CreditCardSupplementary> cards) {
        return cards.stream().filter(card ->
                !card.getAccountStatus().equals(SettingExpConstant.PENDING_ACTIVATION)).toList();
    }
}
