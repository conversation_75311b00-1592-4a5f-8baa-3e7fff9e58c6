package com.tmb.oneapp.settingexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.oneapp.settingexp.client.NotificationServiceClient;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {

    private static final TMBLogger<NotificationServiceImpl> logger = new TMBLogger<>(NotificationServiceImpl.class);

    private final NotificationServiceClient notificationServiceClient;

    @Override
    public void sendNotification(HttpHeaders headers, NotificationRequest request) throws TMBCommonException {
        if (request == null || CollectionUtils.isEmpty(request.getRecords())) {
            logger.error("Notification request payload is missing records");
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD, HttpStatus.BAD_REQUEST);
        }

        String correlationId = headers.getFirst(SettingExpConstant.HEADER_CORRELATION_ID);
        if (StringUtils.isBlank(correlationId)) {
            logger.error("Correlation ID header is missing");
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD, HttpStatus.BAD_REQUEST);
        }

        try {
            notificationServiceClient.sendMessageAsync(correlationId, request);
        } catch (FeignException e) {
            logger.warn("Failed to call notification service asynchronously", e);
        } catch (Exception e) {
            logger.error("Unexpected error while calling notification service asynchronously", e);
        }
    }
}
