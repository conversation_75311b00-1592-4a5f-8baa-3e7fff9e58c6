package com.tmb.oneapp.settingexp.service;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.settingexp.client.OauthFeignClient;
import com.tmb.oneapp.settingexp.model.commonauthen.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.settingexp.utils.FeignClientUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CRM_ID_FOR_COMMON_AUTHEN;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_IP_ADDRESS;

@Service
@RequiredArgsConstructor
public class OauthService {
    private static final TMBLogger<OauthService> logger = new TMBLogger<>(OauthService.class);
    private final OauthFeignClient oauthFeignClient;

    public boolean isVerifyCommonAuthenPass(HttpHeaders headers, CommonAuthenVerifyRefRequest requestCommonAuthen) {
        String crmId = headers.getFirst(HEADER_CRM_ID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        String ipAddress = headers.getFirst(HEADER_IP_ADDRESS);

        boolean isRequireHeadersNull = ObjectUtils.anyNull(crmId, correlationId, ipAddress);
        if (isRequireHeadersNull) {
            logger.error("Headers incorrect, please validate headers. [x-crmid = {}, x-correlation-id = {}, X-Forward-For = {}]", crmId, correlationId, ipAddress);
            return false;
        }

        var requestHeaderCommonAuthen = new org.springframework.http.HttpHeaders();
        requestHeaderCommonAuthen.set(HEADER_CRM_ID_FOR_COMMON_AUTHEN, crmId);
        requestHeaderCommonAuthen.set(HEADER_CORRELATION_ID, correlationId);
        requestHeaderCommonAuthen.set(HEADER_IP_ADDRESS, ipAddress);

        try {
            FeignClientUtils.executeRequest(
                    () -> oauthFeignClient.verifyCommonAuthen(requestHeaderCommonAuthen, requestCommonAuthen)
            );
            return true;

        } catch (Exception e) {
            logger.error("Verify Common-authentication failure, [crm-id = {}, request = {}]", crmId, requestCommonAuthen);
            return false;
        }
    }
}
