package com.tmb.oneapp.settingexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.settingexp.model.pinfree.PinFreeResponse;
import com.tmb.oneapp.settingexp.model.pinfree.UpdatePinFreeSettingsRequest;

/**
 * Service interface for PIN-free functionality
 */
public interface PinFreeService {

    /**
     * Get PIN-free configuration data for a customer
     *
     * @param correlationId the correlation ID from request header
     * @param crmId the CRM ID from request header
     * @return PinFreeResponse containing PIN-free configuration
     */
    PinFreeResponse getPinFreeData(String correlationId, String crmId) throws TMBCommonException;

    /**
     * Update PIN-free settings for a customer.
     *
     * @param correlationId the correlation ID from request header
     * @param crmId the CRM ID from request header
     * @param request payload carrying updated flag and QR limit
     * @return message returned by downstream service
     */
    String updatePinFreeSettings(String correlationId, String crmId, UpdatePinFreeSettingsRequest request) throws TMBCommonException;
}
