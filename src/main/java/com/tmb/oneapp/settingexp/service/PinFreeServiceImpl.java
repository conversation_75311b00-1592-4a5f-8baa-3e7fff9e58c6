package com.tmb.oneapp.settingexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonData;
import com.tmb.oneapp.settingexp.client.CommonServiceClient;
import com.tmb.oneapp.settingexp.client.CustomerServiceClient;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.CustomerCrmProfile;
import com.tmb.oneapp.settingexp.model.activitylog.PinFreeActivityEvent;
import com.tmb.oneapp.settingexp.model.pinfree.CommonAuthenticationInformation;
import com.tmb.oneapp.settingexp.model.pinfree.PinFreeResponse;
import com.tmb.oneapp.settingexp.model.pinfree.UpdatePinFreeSettingsRequest;
import com.tmb.oneapp.settingexp.utils.AsyncUtils;
import com.tmb.oneapp.settingexp.utils.FeignClientUtils;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service
@RequiredArgsConstructor
public class PinFreeServiceImpl implements PinFreeService {

    private static final TMBLogger<PinFreeServiceImpl> logger = new TMBLogger<>(PinFreeServiceImpl.class);

    private final CommonServiceClient commonServiceClient;
    private final CustomerServiceClient customerServiceClient;
    private final AsyncUtils asyncUtils;
    private final EventService eventService;

    @Override
    public PinFreeResponse getPinFreeData(String correlationId, String crmId) throws TMBCommonException {
        logger.info("Getting PIN-free data for correlationId: {}, crmId: {}", correlationId, crmId);

        try {
            CompletableFuture<List<CommonData>> commonConfigFuture = fetchCommonConfigAsync();

            CompletableFuture<CustomerCrmProfile> profileFuture = fetchCustomerProfileAsync(correlationId, crmId);

            CompletableFuture.allOf(commonConfigFuture, profileFuture).get();

            List<CommonData> commonConfigData = getCommonConfigData(commonConfigFuture);

            CustomerCrmProfile profile = getCustomerCrmProfile(profileFuture, crmId);

            logger.info("Successfully retrieved common configuration data");

            if (profile == null) {
                logger.error("Failed to retrieve customer profile data for crmId: {}", crmId);
                throw SettingServiceUtils.getTMBCommonException(ResponseCode.PIN_FREE_DATA_NOT_FOUND, HttpStatus.NOT_FOUND);
            }

            CommonData commonData = extractFirstCommonData(commonConfigData);

            return mapToPinFreeResponse(profile, commonData);

        } catch (ExecutionException e) {
            logger.error("Execution exception getting PIN-free data for correlationId: {}, crmId: {}, error: {}",
                         correlationId, crmId, e.getMessage(), e);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.PIN_FREE_SERVICE_UNAVAILABLE, HttpStatus.SERVICE_UNAVAILABLE);
        } catch (InterruptedException e) {
            logger.error("Interrupted exception getting PIN-free data for correlationId: {}, crmId: {}, error: {}",
                         correlationId, crmId, e.getMessage(), e);
            Thread.currentThread().interrupt();
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.PIN_FREE_SERVICE_UNAVAILABLE, HttpStatus.SERVICE_UNAVAILABLE);
        }
    }

    @Override
    public String updatePinFreeSettings(String correlationId, String crmId, UpdatePinFreeSettingsRequest request) throws TMBCommonException {
        logger.info("Updating PIN-free settings for correlationId: {}, crmId: {}", correlationId, crmId);

        if (request == null) {
            logger.error("Update PIN-free request payload is null for crmId: {}", crmId);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD, HttpStatus.BAD_REQUEST);
        }

        String response = FeignClientUtils.executeRequestWithTMBErrorHandling(
            () -> customerServiceClient.updatePinFreeSettings(crmId, correlationId, request)
        );

        publishPinFreeActivityEvent(correlationId, crmId, request);

        logger.info("Successfully updated PIN-free settings for crmId: {}", crmId);
        return response;
    }

    private void publishPinFreeActivityEvent(String correlationId, String crmId, UpdatePinFreeSettingsRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(SettingExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(SettingExpConstant.HEADER_CRM_ID, crmId);

        PinFreeActivityEvent activityEvent = new PinFreeActivityEvent(headers);

        boolean isPinFreeEnabled = Boolean.TRUE.equals(request.getPinFreeEnableFlag());
        activityEvent.setPinFreeFlag(isPinFreeEnabled ? SettingExpConstant.PIN_FREE_ACTIVITY_STATUS_ON : SettingExpConstant.PIN_FREE_ACTIVITY_STATUS_OFF);

        String scanQrValue = SettingExpConstant.PIN_FREE_SCAN_QR_NOT_AVAILABLE;
        if (isPinFreeEnabled && request.getPinFreeQrLimit() != null) {
            scanQrValue = request.getPinFreeQrLimit().toPlainString();
        }
        activityEvent.setScanQr(scanQrValue);

        eventService.publish(activityEvent);
    }

    private CompletableFuture<List<CommonData>> fetchCommonConfigAsync() {
        return asyncUtils.executeMethodAsync(
            () -> FeignClientUtils.executeRequestSafely(
                () -> commonServiceClient.getCommonConfigByModule(SettingExpConstant.COMMON_MODULE)
            )
        );
    }

    private CompletableFuture<CustomerCrmProfile> fetchCustomerProfileAsync(String correlationId, String crmId) {
        return asyncUtils.executeMethodAsync(
            () -> FeignClientUtils.executeRequestSafely(
                () -> customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId)
            )
        );
    }

    private List<CommonData> getCommonConfigData(CompletableFuture<List<CommonData>> commonConfigFuture) throws ExecutionException, InterruptedException, TMBCommonException {
        List<CommonData> commonConfigData = commonConfigFuture.get();
        boolean isCommonConfigDataMissing = commonConfigData == null || commonConfigData.isEmpty();
        if (isCommonConfigDataMissing) {
            logger.error("Failed to retrieve common configuration data");
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.PIN_FREE_CONFIG_NOT_FOUND, HttpStatus.NOT_FOUND);
        }
        logger.info("Successfully retrieved common configuration data");
        return commonConfigData;
    }

    private CustomerCrmProfile getCustomerCrmProfile(CompletableFuture<CustomerCrmProfile> profileFuture, String crmId) throws ExecutionException, InterruptedException, TMBCommonException {
        CustomerCrmProfile profile = profileFuture.get();
        if (profile == null) {
            logger.error("Failed to retrieve customer profile data for crmId: {}", crmId);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.PIN_FREE_DATA_NOT_FOUND, HttpStatus.NOT_FOUND);
        }
        return profile;
    }

    private CommonData extractFirstCommonData(List<CommonData> commonConfigData) throws TMBCommonException {
        return Optional.ofNullable(commonConfigData)
            .filter(list -> !list.isEmpty())
            .map(list -> list.get(0))
            .orElseThrow(() -> {
                logger.error("Common configuration data first element is null or list is empty");
                return SettingServiceUtils.getTMBCommonException(ResponseCode.PIN_FREE_CONFIG_NOT_FOUND, HttpStatus.NOT_FOUND);
            });
    }

    private PinFreeResponse mapToPinFreeResponse(CustomerCrmProfile profile, CommonData commonConfigResponse) {
        PinFreeResponse response = new PinFreeResponse();

        response.setPinFreeEnableFlag(Boolean.valueOf(profile.getPinFreeSeetingFlag()));
        response.setPinFreeMaxLimit(new BigDecimal(String.valueOf(commonConfigResponse.getPinFreeMaxLimit())));
        response.setPinFreeMaxAccum(new BigDecimal(String.valueOf(commonConfigResponse.getPinFreeMaxAccum())));

        BigDecimal qrLimit = profile.getPinFreeQrLimit();
        boolean isQrLimitNotConfigured = qrLimit == null || qrLimit.compareTo(BigDecimal.ZERO) == 0;
        if (isQrLimitNotConfigured) {
            qrLimit = BigDecimal.valueOf(1000);
        }
        response.setPinFreeQrLimit(qrLimit);

        response.setIsRequireCommonAuthen(true);

        CommonAuthenticationInformation authInfo = new CommonAuthenticationInformation();
        authInfo.setFeatureId(SettingExpConstant.PIN_FREE_FEATURE_ID);
        authInfo.setAmount(BigDecimal.ZERO);
        authInfo.setFlow(SettingExpConstant.PIN_FREE_FLOW);
        authInfo.setDailyAmount(BigDecimal.ZERO);

        response.setCommonAuthenticationInformation(authInfo);

        return response;
    }
}
