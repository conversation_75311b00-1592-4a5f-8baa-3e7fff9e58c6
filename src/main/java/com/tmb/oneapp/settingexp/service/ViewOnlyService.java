package com.tmb.oneapp.settingexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.settingexp.model.manageviewonly.EnableViewOnlyRequest;
import org.springframework.http.HttpHeaders;

public interface ViewOnlyService {
    /**
     * Enable feature View only to set the account cannot make any transaction.
     *
     * @throws TMBCommonException - Verify Common-authentication failed =
     *                            - Cannot updatePersonalizedAcctRelation =
     */

    void enableViewOnly(HttpHeaders httpHeaders, EnableViewOnlyRequest request) throws TMBCommonException;
}
