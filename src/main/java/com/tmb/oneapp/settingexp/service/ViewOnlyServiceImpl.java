package com.tmb.oneapp.settingexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.model.commonauthen.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.settingexp.model.manageviewonly.EnableViewOnlyRequest;
import com.tmb.oneapp.settingexp.model.manageviewonly.UpdateAccountViewOnlyRequest;
import com.tmb.oneapp.settingexp.utils.FeignClientUtils;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.FLOW_NAME_ENABLE_VIEW_ONLY_FOR_COMMON_AUTHEN;

@Service
@RequiredArgsConstructor
public class ViewOnlyServiceImpl implements ViewOnlyService {
    private final CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;
    private final OauthService oauthService;

    @Override
    public void enableViewOnly(HttpHeaders headers, EnableViewOnlyRequest request) throws TMBCommonException {
        validateCommonAuthen(headers, request);

        var updateAccountViewOnlyRequest = new UpdateAccountViewOnlyRequest()
                .setAccountNumber(request.getAccountNumber());
        FeignClientUtils.executeRequestWithTMBErrorHandling(
                () -> customerAccountBizServiceFeignClient.enableViewOnly(updateAccountViewOnlyRequest, headers)
        );
    }

    private void validateCommonAuthen(HttpHeaders headers, EnableViewOnlyRequest request) throws TMBCommonException {
        var requestCommonAuthen = new CommonAuthenVerifyRefRequest()
                .setRefId(request.getFrUuid())
                .setFlowName(FLOW_NAME_ENABLE_VIEW_ONLY_FOR_COMMON_AUTHEN);

        boolean isPass = oauthService.isVerifyCommonAuthenPass(headers, requestCommonAuthen);

        boolean isNotPass = !isPass;
        if (isNotPass) {
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.VERIFY_COMMON_AUTHENTICATION_ERROR);
        }
    }
}
