package com.tmb.oneapp.settingexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;


@Component
public class AsyncUtils {
    @Async
    public <T> CompletableFuture<T> executeRequestAsync(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        return CompletableFuture.completedFuture(FeignClientUtils.executeRequest(supplier));
    }

    @Async
    public <T> CompletableFuture<T> executeRequestAsyncSafely(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) {
        return CompletableFuture.completedFuture(FeignClientUtils.executeRequestSafely(supplier));
    }

    @Async
    public <T> CompletableFuture<T> executeRequestAsyncOrElse(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, T elseAction) {
        return CompletableFuture.completedFuture(FeignClientUtils.executeRequestOrElse(supplier, elseAction));
    }

    @Async
    public <T> CompletableFuture<T> executeMethodAsync(Supplier<T> supplier) {
        return CompletableFuture.completedFuture(supplier.get());
    }

}
