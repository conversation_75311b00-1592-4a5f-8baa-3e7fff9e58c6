package com.tmb.oneapp.settingexp.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import feign.FeignException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.function.Supplier;


public class FeignClientUtils {
    private static final TMBLogger<FeignClientUtils> logger = new TMBLogger<>(FeignClientUtils.class);
    private static final String LOG_EXCEPTION_ERROR_MESSAGE_PATTERN = "Exception encountered while fetching data from {} in method {}.";
    private static final String LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN = "FeignException encountered while fetching data from {} in method {}.";


    private FeignClientUtils() {
        throw new IllegalStateException("Cannot initial FeignClientUtils");
    }

    public static <T> T executeRequestSafely(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) {
        try {
            return executeRequest(supplier);
        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error("Method name {} : execute request without throw then return null. [result = null]", callerMethodName);
            return null;
        }
    }

    public static <T> T executeRequestOrElse(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, T elseAction) {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error("Method name {} : execute request without throw then return else value. [result = {}]", callerMethodName, elseAction.toString());
            return elseAction;
        }
    }

    public static <T, X extends TMBCommonException> T executeRequestOrElseThrow(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, Supplier<? extends X> exceptionSupplier) throws X {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            X customException = exceptionSupplier.get();
            logger.error("Method name {} : execute request throw custom TMBCommonException. [error-code = {}]", callerMethodName, customException.getErrorCode());
            throw customException;
        }
    }

    public static <T> T executeRequestWithTMBErrorHandling(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        try {
            return processExecuteRequest(supplier);
        } catch (FeignException e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw tryToThrowWithTMBErrorCode(e);
        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error(LOG_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, "Exception occurred while fetching data");
        }
    }

    public static <T> T executeRequest(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        try {
            return processExecuteRequest(supplier);
        } catch (FeignException e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, "FeignException occurred while fetching data");
        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error(LOG_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, "Exception occurred while fetching data");
        }
    }

    private static <T> T processExecuteRequest(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        ResponseEntity<TmbServiceResponse<T>> responseEntity = supplier.get();

        if (!responseEntity.getStatusCode().is2xxSuccessful()) {
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, "Non-successful HTTP status code received");
        }

        TmbServiceResponse<T> tmbServiceResponse = responseEntity.getBody();

        if (tmbServiceResponse == null || !ResponseCode.SUCCESS.getCode().equals(tmbServiceResponse.getStatus().getCode())) {
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, "Unsuccessful response received");
        }

        T data = tmbServiceResponse.getData();

        if (data == null) {
            throw SettingServiceUtils.getTMBCommonException(ResponseCode.FAILED, "Null data received");
        }

        return data;
    }

    private static TMBCommonException tryToThrowWithTMBErrorCode(FeignException feignException) throws TMBCommonException {
        Optional<ByteBuffer> byteBufferOptional = feignException.responseBody();
        if (byteBufferOptional.isEmpty()) {
            logger.error("Try to throw with TMBError code cannot read feignException body. [feignException.responseBody = null]");
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
        }

        ByteBuffer byteBuffer = byteBufferOptional.get();
        String s = StandardCharsets.UTF_8.decode(byteBuffer).toString();
        try {
            TmbServiceResponse<?> errorResponse = (TmbServiceResponse<?>) TMBUtils.convertStringToJavaObj(s, TmbServiceResponse.class);

            String errorCode = Optional.of(errorResponse).map(TmbServiceResponse::getStatus).map(Status::getCode).orElse(ResponseCode.FAILED.getCode());

            String ignoreCauseControllerAdviceWillReplaceTheValue = null;
            int httpStatus = feignException.status();
            logger.error("Try to throw with TMBError code [errorCode = {}, httpStatus = {}]", errorCode, httpStatus);
            return new TMBCommonException(errorCode,
                    ignoreCauseControllerAdviceWillReplaceTheValue,
                    ResponseCode.FAILED.getService(),
                    HttpStatus.valueOf(httpStatus), null);
        } catch (JsonProcessingException e) {
            logger.error("Try to throw with TMBError code Cannot parse error mapping. Unknown Response Body.", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.SERVICE_UNAVAILABLE, null);
        }
    }
}
