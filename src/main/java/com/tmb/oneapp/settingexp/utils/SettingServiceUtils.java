package com.tmb.oneapp.settingexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CHANNEL;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_DEVICE_ID;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_DEVICE_MODEL;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_IP_ADDRESS;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.HEADER_OS_VERSION;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.LIST_ALL_ACCOUNT_FLAG;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.REFRESH_FLAG;

public class SettingServiceUtils {
    private static final TMBLogger<SettingServiceUtils> logger = new TMBLogger<>(SettingServiceUtils.class);

    public static Status getStatusSuccess() {
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), null);
    }

    public static TMBCommonException getTMBCommonException(ResponseCode responseCode) {
        logger.error("Throw exception: ({}) reason: {}", responseCode.getCode(), responseCode.getMessage());
        return new TMBCommonException(responseCode.getCode(), responseCode.getMessage(), responseCode.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }
    public static TMBCommonException getTMBCommonException(ResponseCode responseCode, String errorMessage) {
        logger.error("Throw exception: ({}) reason: {}", responseCode.getCode(), errorMessage);
        return new TMBCommonException(responseCode.getCode(), errorMessage, responseCode.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }
    public static TMBCommonException getTMBCommonException(ResponseCode responseCode, String errorMessage, HttpStatus httpStatus) {
        logger.error("Throw exception: ({}) reason: {}", responseCode.getCode(), errorMessage);
        return new TMBCommonException(responseCode.getCode(), errorMessage, responseCode.getService(),
                httpStatus, null);
    }
    public static TMBCommonException getTMBCommonException(ResponseCode responseCode, HttpStatus httpStatus) {
        logger.error("Throw exception: ({}) reason: {}", responseCode.getCode(), responseCode.getMessage());
        return new TMBCommonException(responseCode.getCode(), responseCode.getMessage(), responseCode.getService(),
                httpStatus, null);
    }
    public static HttpHeaders initialOneAppHeader(HttpHeaders headers) {
        HttpHeaders oneAppHeader = new HttpHeaders();
        oneAppHeader.add(HEADER_IP_ADDRESS, headers.getFirst(HEADER_IP_ADDRESS));
        oneAppHeader.add(HEADER_OS_VERSION, headers.getFirst(HEADER_OS_VERSION));
        oneAppHeader.add(HEADER_CHANNEL, headers.getFirst(HEADER_CHANNEL));
        oneAppHeader.add(HEADER_APP_VERSION, headers.getFirst(HEADER_APP_VERSION));
        oneAppHeader.add(HEADER_DEVICE_ID, headers.getFirst(HEADER_DEVICE_ID));
        oneAppHeader.add(HEADER_DEVICE_MODEL, headers.getFirst(HEADER_DEVICE_MODEL));
        oneAppHeader.add(HEADER_CRM_ID, headers.getFirst(HEADER_CRM_ID));
        oneAppHeader.add(HEADER_CORRELATION_ID, headers.getFirst(HEADER_CORRELATION_ID));
        oneAppHeader.add(REFRESH_FLAG, headers.getFirst(REFRESH_FLAG));
        oneAppHeader.add(LIST_ALL_ACCOUNT_FLAG, headers.getFirst(LIST_ALL_ACCOUNT_FLAG));

        return oneAppHeader;
    }
}
