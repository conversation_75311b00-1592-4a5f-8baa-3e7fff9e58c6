#================= START Mandatory config =================
server.port=8080
spring.application.name=setting-exp
spring.application.description=
oneapp.ocp.domain=dev4-oneapp.svc
utility.common.service.endpoint=http://common-service-https-internal-dev1-oneapp.apps.ddid1.tmbcps.com

spring.redis.mode=standalone

kafka.prefix.topic=
spring.kafka.jaas.options.username=appusr
spring.kafka.jaas.options.password=
spring.kafka.producer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092

private.key.location=keys/rsa_private.key
public.key.location=keys/rsa_public.key

##ActivityConfig
setting.exp.service.activity.topic.name=${kafka.prefix.topic}activity
#================= END Mandatory config =================

#================= START Feign group =================
# Customers-exp-service feign client
feign.customers.exp.service.name=customers-exp-service
feign.customers.exp.service.url=http://${feign.customers.exp.service.name}.${oneapp.ocp.domain}

#Customer-account-biz
feign.customer.account.biz.service.name=customer-account-biz
feign.customer.account.biz.service.url=http://${feign.customer.account.biz.service.name}.${oneapp.ocp.domain}

#oauth service
feign.auth.service.name=oneapp-auth-service
feign.auth.service.url=http://${feign.auth.service.name}.${oneapp.ocp.domain}

#Customers-service
feign.customers.service.name=customers-service
feign.customers.service.url=http://${feign.customers.service.name}.${oneapp.ocp.domain}

#Bank-service
feign.bank.service.name=bank-service
feign.bank.service.url=http://${feign.bank.service.name}.${oneapp.ocp.domain}

#Retail-lending-biz feign client
feign.retail.lending.biz.name=retail-lending-biz
feign.retail.lending.biz.url=http://${feign.retail.lending.biz.name}.${oneapp.ocp.domain}

#Notification-service
feign.notification.service.name=notification-service
feign.notification.service.url=http://${feign.notification.service.name}.${oneapp.ocp.domain}
#================= END Feign group =================

#================= START Redis Config ================
redis.cache.enabled=true
redis.cache.custom.ttl=true
redis.template.enabled=true
#================= END Redis Config ================

#================= START Other config =================
#Add Inbound/OutBound Log
app.api.logging.enable=true
app.api.logging.max-length=10000
app.api.logging.url-patterns=*
app.api.logging.feign.enable=true
app.api.logging.feign.exclude-url-patterns=
#================= END Other config =================