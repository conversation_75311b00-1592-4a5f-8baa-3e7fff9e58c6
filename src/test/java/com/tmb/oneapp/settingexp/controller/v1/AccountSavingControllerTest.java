package com.tmb.oneapp.settingexp.controller.v1;

import com.tmb.oneapp.settingexp.model.AccountSaving;
import com.tmb.oneapp.settingexp.service.AccountSavingServiceImpl;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

@ExtendWith(MockitoExtension.class)
class AccountSavingControllerTest {
    @InjectMocks
    AccountSavingController accountSavingController;

    @Mock
    AccountSavingServiceImpl accountSavingServiceImpl;

    @BeforeEach
    void setUp() {
    }

    @Test
    void getAccountsSavingSuccessTest() {
        Mockito.when(accountSavingServiceImpl.getAccountSaving(new HttpHeaders())).thenReturn(new AccountSaving());

        Assertions.assertDoesNotThrow(() -> accountSavingController.getAccountsSaving(new HttpHeaders()));
    }

    @Test
    void getReOrderAccountThrowsTest() {
        Mockito.when(accountSavingServiceImpl.getAccountSaving(new HttpHeaders())).thenThrow(FeignException.class);

        Assertions.assertThrows(FeignException.class, () -> accountSavingController.getAccountsSaving(new HttpHeaders()));
    }
}