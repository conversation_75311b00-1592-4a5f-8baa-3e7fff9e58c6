package com.tmb.oneapp.settingexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.model.AccountSortOrderRequest;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRPostAddAccountsServiceRequest;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardAccount;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardFormatedResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardSupplementary;
import com.tmb.oneapp.settingexp.model.manageviewonly.EnableViewOnlyRequest;
import com.tmb.oneapp.settingexp.service.ManageAccountService;
import com.tmb.oneapp.settingexp.service.ViewOnlyService;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class ManageAccountControllerTest {
    @InjectMocks
    ManageAccountController manageAccountController;

    @Mock
    CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;
    @Mock
    ManageAccountService manageAccountService;
    @Mock
    ViewOnlyService viewOnlyService;

    @BeforeEach
    void setUp() {
    }

    @Test
    void getReOrderAccountSuccessTest() {
        Mockito.when(customerAccountBizServiceFeignClient.updateReorderAccount(any(), any())).thenReturn(ResponseEntity.ok(new TmbServiceResponse<>()));

        Assertions.assertDoesNotThrow(() -> manageAccountController.updatePersonalizedAccountSortOrder("correlation-id", "crm-id", List.of(new AccountSortOrderRequest()), new HttpHeaders()));
    }

    @Test
    void getReOrderAccountThrowsTest() {
        Mockito.when(customerAccountBizServiceFeignClient.updateReorderAccount(any(), any())).thenThrow(FeignException.class);

        Assertions.assertThrows(FeignException.class, () -> manageAccountController.updatePersonalizedAccountSortOrder("correlation-id", "crm-id", List.of(new AccountSortOrderRequest()), new HttpHeaders()));
    }

    @Test
    void postAccountsSavingAddCommonFRSuccessTest() throws TMBCommonException {
        Mockito.doNothing().when(manageAccountService).postAccountsSavingAddCommonFR(any(), any());

        Assertions.assertDoesNotThrow(() -> manageAccountController.postAccountsSavingAddCommonFR(new HttpHeaders(), new CommonFRPostAddAccountsServiceRequest()));
    }

    @Test
    void postAccountsSavingAddCommonFRThrowsTest() throws TMBCommonException {
        Mockito.doThrow(TMBCommonException.class).when(manageAccountService).postAccountsSavingAddCommonFR(any(), any());

        Assertions.assertThrows(TMBCommonException.class, () -> manageAccountController.postAccountsSavingAddCommonFR(new HttpHeaders(), new CommonFRPostAddAccountsServiceRequest()));
    }

    @Test
    void manageCreditCardSuccessTest() throws TMBCommonException {
        CreditCardSupplementary creditCard = new CreditCardSupplementary();
        creditCard.setAccountStatus("active");
        CreditCardSupplementary flashCard = new CreditCardSupplementary();
        flashCard.setAccountStatus("active");

        CreditCardFormatedResponse creditCardResponse = new CreditCardFormatedResponse();
        creditCardResponse.setCreditCards(Collections.singletonList(creditCard));
        creditCardResponse.setFlashCards(Collections.singletonList(flashCard));

        Mockito.doReturn(creditCardResponse).when(manageAccountService).getCreditCardList(any());

        Assertions.assertDoesNotThrow(() -> manageAccountController.manageCreditCard(new HttpHeaders()));
    }

    @Test
    void manageCreditCardThrowsTest() throws TMBCommonException {
        Mockito.doThrow(TMBCommonException.class).when(manageAccountService).getCreditCardList(any());

        Assertions.assertThrows(TMBCommonException.class, () -> manageAccountController.manageCreditCard(new HttpHeaders()));
    }

    @Test
    void manageViewOnlySuccessTest() throws TMBCommonException {
        Mockito.doNothing().when(viewOnlyService).enableViewOnly(any(), any(EnableViewOnlyRequest.class));

        Assertions.assertDoesNotThrow(() -> manageAccountController.enableViewOnly("correlation-id", "crm-id", new HttpHeaders(), new EnableViewOnlyRequest()));
    }

    @Test
    void manageViewOnlyThrowsTest() throws TMBCommonException {
        Mockito.doThrow(TMBCommonException.class).when(viewOnlyService).enableViewOnly(any(), any(EnableViewOnlyRequest.class));

        Assertions.assertThrows(TMBCommonException.class, () -> manageAccountController.enableViewOnly("correlation-id", "crm-id", new HttpHeaders(), new EnableViewOnlyRequest()));
    }
}