package com.tmb.oneapp.settingexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.model.pinfree.PinFreeResponse;
import com.tmb.oneapp.settingexp.model.pinfree.UpdatePinFreeSettingsRequest;
import com.tmb.oneapp.settingexp.service.PinFreeService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PinFreeControllerTest {

    private static final String CORRELATION_ID = "corr-id";
    private static final String CRM_ID = "crm-id";

    @Mock
    private PinFreeService pinFreeService;

    @InjectMocks
    private PinFreeController pinFreeController;

    @Test
    void updatePinFreeSettings_ShouldReturnSuccessResponse() throws TMBCommonException {
        UpdatePinFreeSettingsRequest request = new UpdatePinFreeSettingsRequest(true, BigDecimal.valueOf(500));

        when(pinFreeService.updatePinFreeSettings(CORRELATION_ID, CRM_ID, request)).thenReturn("success");

        ResponseEntity<TmbServiceResponse<String>> response = pinFreeController.updatePinFreeSettings(CORRELATION_ID, CRM_ID, request);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
        Assertions.assertEquals("success", response.getBody().getData());
        Assertions.assertEquals(ResponseCode.SUCCESS.getCode(), response.getBody().getStatus().getCode());

        Mockito.verify(pinFreeService).updatePinFreeSettings(CORRELATION_ID, CRM_ID, request);
    }

    @Test
    void updatePinFreeSettings_WhenServiceThrows_ShouldPropagateException() throws TMBCommonException {
        UpdatePinFreeSettingsRequest request = new UpdatePinFreeSettingsRequest(false, BigDecimal.valueOf(750));

        TMBCommonException expected = new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null
        );

        when(pinFreeService.updatePinFreeSettings(CORRELATION_ID, CRM_ID, request)).thenThrow(expected);

        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class,
                () -> pinFreeController.updatePinFreeSettings(CORRELATION_ID, CRM_ID, request));

        Assertions.assertEquals(ResponseCode.FAILED.getCode(), actual.getErrorCode());
    }

    @Test
    void getPinFreeConfig_ShouldReturnServiceResponse() throws TMBCommonException {
        PinFreeResponse pinFreeResponse = new PinFreeResponse();

        when(pinFreeService.getPinFreeData(CORRELATION_ID, CRM_ID)).thenReturn(pinFreeResponse);

        ResponseEntity<TmbServiceResponse<PinFreeResponse>> response = pinFreeController.getPinFreeConfig(CORRELATION_ID, CRM_ID);

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
        Assertions.assertEquals(pinFreeResponse, response.getBody().getData());
        Assertions.assertEquals(ResponseCode.SUCCESS.getCode(), response.getBody().getStatus().getCode());

        Mockito.verify(pinFreeService).getPinFreeData(CORRELATION_ID, CRM_ID);
    }
}
