package com.tmb.oneapp.settingexp.service;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.client.BankServiceClient;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.AccountSaving;
import com.tmb.oneapp.settingexp.model.BranchDataModel;
import com.tmb.oneapp.settingexp.model.DepositAccount;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.LIST_ALL_ACCOUNT_FLAG_VALUE_TRUE;
import static com.tmb.oneapp.settingexp.constant.SettingExpConstant.REFRESH_FLAG_VALUE_TRUE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountSavingServiceImplTest {
    @InjectMocks
    AccountSavingServiceImpl accountSavingServiceImpl;

    @Mock
    CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;
    @Mock
    BankServiceClient bankServiceClient;

    HttpHeaders headers;
    List<BranchDataModel> branchDataModelList;

    @BeforeEach
    void setUp() {
        headers = new HttpHeaders();
        headers.add(SettingExpConstant.HEADER_CORRELATION_ID, "correlationId");

        branchDataModelList = new ArrayList<>();
    }

    @Test
    void getAccountSavingShouldSetBranchNameCorrectlySuccessTest() {
        mockGetAccountSaving();
        mockGetBranch();

        AccountSaving actual = accountSavingServiceImpl.getAccountSaving(headers);

        List<DepositAccount> actualDeposit = actual.getDepositAccountLists();
        Assertions.assertEquals("1111", actualDeposit.get(0).getBranchCode());
        Assertions.assertEquals("nameEN-1111", actualDeposit.get(0).getBranchNameEn());
        Assertions.assertEquals(branchDataModelList.get(1).getBranchCode(), actualDeposit.get(1).getBranchCode());
        Assertions.assertEquals(branchDataModelList.get(1).getBranchNameEn(), actualDeposit.get(1).getBranchNameEn());
        List<DepositAccount> actualFcd = actual.getFcdAccountLists();
        Assertions.assertEquals("3333", actualFcd.get(0).getBranchCode());
        Assertions.assertEquals("nameEN-3333", actualFcd.get(0).getBranchNameEn());
        Assertions.assertEquals(branchDataModelList.get(3).getBranchCode(), actualFcd.get(1).getBranchCode());
        Assertions.assertEquals(branchDataModelList.get(3).getBranchNameEn(), actualFcd.get(1).getBranchNameEn());
    }

    @Test
    void getAccountSavingWhenCannotGetAccountSavingShouldThrowTest() {
        when(customerAccountBizServiceFeignClient.getAccountsSaving(any(HttpHeaders.class), eq(LIST_ALL_ACCOUNT_FLAG_VALUE_TRUE), eq(null))).thenThrow(FeignException.class);

        Assertions.assertThrows(FeignException.class, () -> accountSavingServiceImpl.getAccountSaving(headers));
    }

    @Test
    void getAccountSavingWhenCannotGetBranchShouldThrowTest() {
        mockGetAccountSaving();
        when(bankServiceClient.getBranch(anyString())).thenThrow(FeignException.class);

        Assertions.assertThrows(FeignException.class, () -> accountSavingServiceImpl.getAccountSaving(headers));
    }

    private void mockGetBranch() {
        branchDataModelList.add(new BranchDataModel().setBranchCode("1111").setBranchNameEn("nameEN-1111").setBranchNameTh("nameTH-1111"));
        branchDataModelList.add(new BranchDataModel().setBranchCode("2222").setBranchNameEn("nameEN-2222").setBranchNameTh("nameTH-2222"));
        branchDataModelList.add(new BranchDataModel().setBranchCode("3333").setBranchNameEn("nameEN-3333").setBranchNameTh("nameTH-3333"));
        branchDataModelList.add(new BranchDataModel().setBranchCode("4444").setBranchNameEn("nameEN-4444").setBranchNameTh("nameTH-4444"));
        branchDataModelList.add(new BranchDataModel().setBranchCode("5555").setBranchNameEn("nameEN-5555").setBranchNameTh("nameTH-5555"));
        branchDataModelList.add(new BranchDataModel().setBranchCode("6666").setBranchNameEn("nameEN-6666").setBranchNameTh("nameTH-6666"));

        TmbOneServiceResponse<List<BranchDataModel>> tmbServiceResponse = new TmbOneServiceResponse<>();
        tmbServiceResponse.setData(branchDataModelList);
        when(bankServiceClient.getBranch(anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockGetAccountSaving() {
        AccountSaving accountSaving = new AccountSaving();
        accountSaving.setDepositAccountLists(new ArrayList<>());
        accountSaving.setFcdAccountLists(new ArrayList<>());

        accountSaving.getDepositAccountLists().add(new DepositAccount().setAccountNumber("**********").setBranchCode("1111"));
        accountSaving.getDepositAccountLists().add(new DepositAccount().setAccountNumber("**********").setBranchCode("2222"));

        accountSaving.getFcdAccountLists().add(new DepositAccount().setAccountNumber("**********").setBranchCode("3333"));
        accountSaving.getFcdAccountLists().add(new DepositAccount().setAccountNumber("**********").setBranchCode("4444"));

        TmbServiceResponse<AccountSaving> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(accountSaving);
        when(customerAccountBizServiceFeignClient.getAccountsSaving(any(HttpHeaders.class), eq(LIST_ALL_ACCOUNT_FLAG_VALUE_TRUE), eq(null))).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }
}