package com.tmb.oneapp.settingexp.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.model.BaseEvent;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.KafkaException;

@ExtendWith(MockitoExtension.class)
class EventServiceTest {
    @InjectMocks
    EventService eventService;

    @Mock
    KafkaProducerService kafkaProducerService;


    @Test
    void publishSuccessTest() {
        BaseEvent event = new BaseEvent();

        boolean actual = eventService.publish(event);

        Assertions.assertTrue(actual);
    }

    @Test
    void publishFailedTest() {
        BaseEvent event = new BaseEvent();
        Mockito.doThrow(KafkaException.class).when(kafkaProducerService).sendMessageAsync(Mockito.any(), Mockito.any());

        boolean actual = eventService.publish(event);

        Assertions.assertFalse(actual);
    }

}