package com.tmb.oneapp.settingexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.cache.service.RedisUtilService;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.client.CustomerServiceClient;
import com.tmb.oneapp.settingexp.client.RetailLendingBizClient;
import com.tmb.oneapp.settingexp.constant.DisplayAccountStatusConstants;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.AddDepositAccountRequest;
import com.tmb.oneapp.settingexp.model.VerifyDepositAccount;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRPostAddAccountsServiceRequest;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRVerifyFRRequest;
import com.tmb.oneapp.settingexp.model.commonfr.CommonFRVerifyFRResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CardStatus;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardAccount;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardFormatedResponse;
import com.tmb.oneapp.settingexp.model.managecreditcard.CreditCardSupplementary;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import feign.FeignException;
import feign.Request;
import feign.RequestTemplate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ManageAccountServiceImplTest {
    @InjectMocks
    ManageAccountServiceImpl manageAccountServiceImpl;

    @Mock
    CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;
    @Mock
    CustomerServiceClient customerServiceClient;
    @Mock
    RetailLendingBizClient retailLendingBizClient;
    @Mock
    RedisUtilService redisUtilService;
    @Mock
    EventService eventService;

    HttpHeaders headers;

    @BeforeEach
    void setUp() {
        headers = new HttpHeaders();
        headers.set(SettingExpConstant.HEADER_CRM_ID, "crmId");
        headers.set(SettingExpConstant.HEADER_CORRELATION_ID, "correlationId");
        headers.set(SettingExpConstant.HEADER_ACCEPT_LANGUAGE, "th");
        headers.set(SettingExpConstant.HEADER_APP_VERSION, "4.0.0");
        headers.set(SettingExpConstant.HEADER_IP_ADDRESS, "ipAddress");
    }

    private CommonFRPostAddAccountsServiceRequest mockAccountsServiceAddCommonFRRequest() {
        CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest = new CommonFRPostAddAccountsServiceRequest();
        commonFRPostAddAccountsServiceRequest.setUuid("uuid");
        commonFRPostAddAccountsServiceRequest.setVerifyAccountRefId("verifyAccountRef");
        commonFRPostAddAccountsServiceRequest.setAccountNickname("all free account");
        commonFRPostAddAccountsServiceRequest.setFeatureId(1003);
        commonFRPostAddAccountsServiceRequest.setFlow("flow");
        return commonFRPostAddAccountsServiceRequest;
    }

    private void mockAddDepositAccountSuccess() {
        TmbServiceResponse<String> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(SettingServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData("Success");
        when(customerAccountBizServiceFeignClient.addDepositAccount(any(AddDepositAccountRequest.class), any(HttpHeaders.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockPostCommonFRVerifyFRSuccess() {
        TmbServiceResponse<CommonFRVerifyFRResponse> response = new TmbServiceResponse<>();
        CommonFRVerifyFRResponse commonFRVerifyFRResponse = new CommonFRVerifyFRResponse();
        commonFRVerifyFRResponse.setUuid("uuid");
        commonFRVerifyFRResponse.setCommonfrSuccess(Boolean.TRUE);
        commonFRVerifyFRResponse.setCreateDate(LocalDateTime.now().toString());

        response.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
        response.setData(commonFRVerifyFRResponse);
        when(customerServiceClient.postCommonFRVerifyFR(anyString(), anyString(), anyString(), anyString(), anyString(), any(CommonFRVerifyFRRequest.class)))
                .thenReturn(ResponseEntity.ok(response));
    }

    private void mockPostCommonFRVerifyFRReturnUUIDNull() {
        TmbServiceResponse<CommonFRVerifyFRResponse> response = new TmbServiceResponse<>();
        CommonFRVerifyFRResponse commonFRVerifyFRResponse = new CommonFRVerifyFRResponse();
        commonFRVerifyFRResponse.setUuid(null);
        commonFRVerifyFRResponse.setCommonfrSuccess(Boolean.TRUE);
        commonFRVerifyFRResponse.setCreateDate(LocalDateTime.now().toString());

        response.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
        response.setData(commonFRVerifyFRResponse);
        when(customerServiceClient.postCommonFRVerifyFR(anyString(), anyString(), anyString(), anyString(), anyString(), any(CommonFRVerifyFRRequest.class))).thenReturn(ResponseEntity.ok(response));
    }

    private FeignException.BadRequest feignBadRequestEx(byte[] data) {
        Map<String, Collection<String>> headers = new HashMap<>();
        RequestTemplate requestTemplate = new RequestTemplate();
        Request request = Request.create(Request.HttpMethod.POST, "https://oneapp-dev1.tau2904.com/v1/customers-service/common-fr/request", headers, null, requestTemplate);
        return new FeignException.BadRequest("Fail", request, data, null);
    }

    private <T> TmbServiceResponse<T> mockError400Response(ResponseCode responseCode) {
        TmbServiceResponse<T> response = new TmbServiceResponse<>();
        response.setStatus(new Status(responseCode.getCode(), responseCode.getMessage(), responseCode.getService(), null));
        response.setData(null);

        return response;
    }

    @Test
    void test_postAccountsSavingAddCommonFR_ShouldSuccess() {
        CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest = mockAccountsServiceAddCommonFRRequest();
        mockPostCommonFRVerifyFRSuccess();
        mockAddDepositAccountSuccess();

        Assertions.assertDoesNotThrow(() -> manageAccountServiceImpl.postAccountsSavingAddCommonFR(headers, commonFRPostAddAccountsServiceRequest));
    }

    @Test
    void test_postAccountsSavingAddCommonFR_ShouldThrowFeignException_postCommonFRVerifyFR() throws JsonProcessingException {
        CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest = mockAccountsServiceAddCommonFRRequest();
        FeignException.BadRequest exception = feignBadRequestEx(new ObjectMapper().writeValueAsBytes(mockError400Response(ResponseCode.FAILED)));
        when(customerServiceClient.postCommonFRVerifyFR(anyString(), anyString(), anyString(), anyString(), anyString(), any(CommonFRVerifyFRRequest.class)))
                .thenThrow(exception);

        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () ->
                manageAccountServiceImpl.postAccountsSavingAddCommonFR(headers, commonFRPostAddAccountsServiceRequest));

        Assertions.assertEquals(ResponseCode.COMMON_FR_GENERIC_ERROR.getCode(), actual.getErrorCode());
        Assertions.assertEquals(ResponseCode.COMMON_FR_GENERIC_ERROR.getMessage(), actual.getErrorMessage());
    }

    @Test
    void test_postAccountsSavingAddCommonFR_ShouldThrow8000_UuidFromCommonFRVerifyIsNull() throws JsonProcessingException {
        VerifyDepositAccount verifyAccount = new VerifyDepositAccount();
        verifyAccount.setAccountNo("**********");
        verifyAccount.setAccountType("Deposit");
        verifyAccount.setDisplayAccountStatus("Add");
        verifyAccount.setBranch("-");
        verifyAccount.setCrmId("");

        mockPostCommonFRVerifyFRReturnUUIDNull();
        when(redisUtilService.get(anyString())).thenReturn(TMBUtils.convertJavaObjectToString(verifyAccount));

        CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest = mockAccountsServiceAddCommonFRRequest();
        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () -> manageAccountServiceImpl.postAccountsSavingAddCommonFR(headers, commonFRPostAddAccountsServiceRequest));

        Assertions.assertEquals(ResponseCode.COMMON_FR_GENERIC_ERROR.getCode(), actual.getErrorCode());
        Assertions.assertEquals(ResponseCode.COMMON_FR_GENERIC_ERROR.getMessage(), actual.getErrorMessage());
        Mockito.verify(eventService, times(1)).publish(any());
    }

    @Test
    void test_postAccountsSavingAddCommonFR_ShouldThrow8000_UuidFromCommonFRVerifyIsNullAndCannotGetAccountFromCache() {
        mockPostCommonFRVerifyFRReturnUUIDNull();
        when(redisUtilService.get(anyString())).thenReturn(null);

        CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest = mockAccountsServiceAddCommonFRRequest();
        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () -> manageAccountServiceImpl.postAccountsSavingAddCommonFR(headers, commonFRPostAddAccountsServiceRequest));

        Assertions.assertEquals(ResponseCode.FAILED.getCode(), actual.getErrorCode());
        Assertions.assertEquals(DisplayAccountStatusConstants.ERROR_ADD_DEPOSIT_ACCOUNT_WITHOUT_VERIFY_DES, actual.getErrorMessage());
        Mockito.verify(eventService, times(0)).publish(any());
    }

    @Test
    void test_postAccountsSavingAddCommonFR_ShouldThrow8000_UuidFromCommonFRVerifyIsNullAndCannotParseObjectAccountFromCache() {
        mockPostCommonFRVerifyFRReturnUUIDNull();
        when(redisUtilService.get(anyString())).thenReturn("Incorrect object");

        CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest = mockAccountsServiceAddCommonFRRequest();
        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () -> manageAccountServiceImpl.postAccountsSavingAddCommonFR(headers, commonFRPostAddAccountsServiceRequest));

        Assertions.assertEquals(ResponseCode.FAILED.getCode(), actual.getErrorCode());
        Mockito.verify(eventService, times(0)).publish(any());
    }

    @Test
    void test_postAccountsSavingAddCommonFR_ShouldThrowTMBCommonException_addDepositAccountWithoutValidatePinRef() throws JsonProcessingException {
        CommonFRPostAddAccountsServiceRequest commonFRPostAddAccountsServiceRequest = mockAccountsServiceAddCommonFRRequest();

        mockPostCommonFRVerifyFRSuccess();

        FeignException.BadRequest exception = feignBadRequestEx(new ObjectMapper().writeValueAsBytes(mockError400Response(ResponseCode.FAILED)));
        when(customerAccountBizServiceFeignClient.addDepositAccount(any(AddDepositAccountRequest.class), any(HttpHeaders.class))).thenThrow(exception);

        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () -> manageAccountServiceImpl.postAccountsSavingAddCommonFR(headers, commonFRPostAddAccountsServiceRequest));

        Assertions.assertEquals(ResponseCode.COMMON_FR_GENERIC_ERROR.getCode(), actual.getErrorCode());
        Assertions.assertEquals(ResponseCode.COMMON_FR_GENERIC_ERROR.getMessage(), actual.getErrorMessage());
    }

    @Test
    void test_getCreditCardList_Success() throws TMBCommonException {
        CreditCardSupplementary creditCard = new CreditCardSupplementary();
        creditCard.setAccountStatus("active");
        creditCard.setExpiredBy("2403");
        creditCard.setAccountId("1234");
        CardStatus cardStatus = new CardStatus();
        cardStatus.setPreviousExpiryDate("0000");
        creditCard.setCardStatus(cardStatus);

        CreditCardSupplementary flashCard = new CreditCardSupplementary();
        flashCard.setAccountStatus("pending_activation");

        CreditCardFormatedResponse creditCardResponse = new CreditCardFormatedResponse();
        creditCardResponse.setCreditCards(Collections.singletonList(creditCard));
        creditCardResponse.setFlashCards(Collections.EMPTY_LIST);

        TmbOneServiceResponse response = new TmbOneServiceResponse();
        response.setData(creditCardResponse);

        Mockito.doReturn(ResponseEntity.ok(response)).when(retailLendingBizClient).getCreditCard(headers);

        CreditCardFormatedResponse actual = manageAccountServiceImpl.getCreditCardList(headers);
        Assertions.assertEquals(1, actual.getCreditCards().size());
        Assertions.assertEquals("03/24", actual.getCreditCards().get(0).getDisplayExpireDate());
        Assertions.assertTrue(actual.getFlashCards().isEmpty());
    }

    @Test
    void test_getCreditCardList_Success_When_IsRenewalCardExist() throws TMBCommonException {
        CreditCardAccount flashCard = new CreditCardAccount();
        flashCard.setAccountStatus("pending_activation");

        CreditCardFormatedResponse creditCardResponse = new CreditCardFormatedResponse();
        creditCardResponse.setCreditCards(this.mockCreditCardList("active"));
        creditCardResponse.setFlashCards(Collections.EMPTY_LIST);

        TmbOneServiceResponse response = new TmbOneServiceResponse();
        response.setData(creditCardResponse);

        Mockito.doReturn(ResponseEntity.ok(response)).when(retailLendingBizClient).getCreditCard(headers);

        CreditCardFormatedResponse actual = manageAccountServiceImpl.getCreditCardList(headers);
        Assertions.assertEquals(1, actual.getCreditCards().size());
        Assertions.assertEquals("03/21", actual.getCreditCards().get(0).getDisplayExpireDate());
        Assertions.assertTrue(actual.getFlashCards().isEmpty());
    }

    @Test
    void test_getCreditCardList_Success_When_IsRenewalCardExistWithStatusTemporaryBlock() throws TMBCommonException {
        CreditCardFormatedResponse creditCardResponse = new CreditCardFormatedResponse();
        creditCardResponse.setCreditCards(this.mockCreditCardList("temporary_block"));
        creditCardResponse.setFlashCards(Collections.EMPTY_LIST);

        TmbOneServiceResponse response = new TmbOneServiceResponse();
        response.setData(creditCardResponse);

        Mockito.doReturn(ResponseEntity.ok(response)).when(retailLendingBizClient).getCreditCard(headers);

        CreditCardFormatedResponse actual = manageAccountServiceImpl.getCreditCardList(headers);
        Assertions.assertEquals(1, actual.getCreditCards().size());
        Assertions.assertEquals("03/21", actual.getCreditCards().get(0).getDisplayExpireDate());
        Assertions.assertTrue(actual.getFlashCards().isEmpty());
    }

    @Test
    void test_getCreditCardList_Failed_WhenInvalidRequest() {
        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () ->
                manageAccountServiceImpl.getCreditCardList(new HttpHeaders()));

        Assertions.assertEquals("0001", actual.getErrorCode());
        Assertions.assertEquals("Value must not be null", actual.getErrorMessage());
    }

    @Test
    void test_getCreditCardList_Failed_WhenDataNotFound() {
        CreditCardFormatedResponse creditCardResponse = new CreditCardFormatedResponse();
        creditCardResponse.setCreditCards(Collections.EMPTY_LIST);

        TmbOneServiceResponse response = new TmbOneServiceResponse();
        response.setData(creditCardResponse);

        Mockito.doReturn(ResponseEntity.ok(response)).when(retailLendingBizClient).getCreditCard(headers);

        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () ->
                manageAccountServiceImpl.getCreditCardList(headers));

        Assertions.assertEquals("0009", actual.getErrorCode());
        Assertions.assertEquals("Data not found", actual.getErrorMessage());
    }

    private List<CreditCardSupplementary> mockCreditCardList(String accountStatus) {
        CardStatus status = new CardStatus();
        status.setPreviousExpiryDate("2103");

        CreditCardSupplementary creditCard = new CreditCardSupplementary();
        creditCard.setAccountStatus(accountStatus);
        creditCard.setExpiredBy("2403");
        creditCard.setAccountId("1234");
        creditCard.setCardStatus(status);

        CreditCardSupplementary creditCard2 = new CreditCardSupplementary();
        creditCard2.setAccountStatus("pending_activation");
        creditCard2.setExpiredBy("2403");
        creditCard2.setAccountId("1234");
        creditCard2.setCardStatus(status);

        return List.of(creditCard, creditCard2);
    }
}