package com.tmb.oneapp.settingexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.request.notification.NotificationRecord;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.common.model.response.notification.NotificationResponse;
import com.tmb.oneapp.settingexp.client.NotificationServiceClient;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationServiceImplTest {

    private static final String CORRELATION_ID = "corr-id";

    @Mock
    private NotificationServiceClient notificationServiceClient;

    @InjectMocks
    private NotificationServiceImpl notificationService;

    @Test
    void sendNotification_shouldInvokeAsyncEndpoint_whenDownstreamSuccess() throws TMBCommonException {
        HttpHeaders headers = buildHeaders();
        NotificationRequest request = buildRequest();

        when(notificationServiceClient.sendMessageAsync(CORRELATION_ID, request))
                .thenReturn(ResponseEntity.accepted().build());

        Assertions.assertDoesNotThrow(() -> notificationService.sendNotification(headers, request));

        verify(notificationServiceClient).sendMessageAsync(CORRELATION_ID, request);
    }

    @Test
    void sendNotification_shouldThrowException_whenRecordsMissing() {
        HttpHeaders headers = buildHeaders();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> notificationService.sendNotification(headers, new NotificationRequest()));

        Assertions.assertEquals(ResponseCode.MISSING_REQUIRED_FIELD.getCode(), exception.getErrorCode());
        verifyNoInteractions(notificationServiceClient);
    }

    @Test
    void sendNotification_shouldThrowException_whenCorrelationIdMissing() {
        HttpHeaders headers = new HttpHeaders();
        NotificationRequest request = buildRequest();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> notificationService.sendNotification(headers, request));

        Assertions.assertEquals(ResponseCode.MISSING_REQUIRED_FIELD.getCode(), exception.getErrorCode());
        verifyNoInteractions(notificationServiceClient);
    }

    @Test
    void sendNotification_shouldSwallowDownstreamFailureStatus() {
        HttpHeaders headers = buildHeaders();
        NotificationRequest request = buildRequest();

        TmbServiceResponse<NotificationResponse> body = new TmbServiceResponse<>();
        body.setStatus(new Status(ResponseCode.FAILED.getCode(), "downstream failed", ResponseCode.FAILED.getService(), null));
        body.setData(null);

        when(notificationServiceClient.sendMessageAsync(CORRELATION_ID, request))
                .thenReturn(ResponseEntity.ok(body));

        Assertions.assertDoesNotThrow(() -> notificationService.sendNotification(headers, request));
    }

    @Test
    void sendNotification_shouldSwallowEmptyBody() {
        HttpHeaders headers = buildHeaders();
        NotificationRequest request = buildRequest();

        when(notificationServiceClient.sendMessageAsync(CORRELATION_ID, request))
                .thenReturn(ResponseEntity.ok().build());

        Assertions.assertDoesNotThrow(() -> notificationService.sendNotification(headers, request));
    }

    @Test
    void sendNotification_shouldSwallowNon2xxHttpStatus() {
        HttpHeaders headers = buildHeaders();
        NotificationRequest request = buildRequest();

        when(notificationServiceClient.sendMessageAsync(CORRELATION_ID, request))
                .thenReturn(ResponseEntity.status(502).build());

        Assertions.assertDoesNotThrow(() -> notificationService.sendNotification(headers, request));
    }

    @Test
    void sendNotification_shouldSwallowFeignException() {
        HttpHeaders headers = buildHeaders();
        NotificationRequest request = buildRequest();

        when(notificationServiceClient.sendMessageAsync(CORRELATION_ID, request))
                .thenThrow(mock(feign.FeignException.class));

        Assertions.assertDoesNotThrow(() -> notificationService.sendNotification(headers, request));
    }

    private static HttpHeaders buildHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set(SettingExpConstant.HEADER_CORRELATION_ID, CORRELATION_ID);
        return headers;
    }

    private static NotificationRequest buildRequest() {
        NotificationRecord record = new NotificationRecord();
        record.setCrmId("crm");
        record.setParams(new HashMap<>());

        NotificationRequest request = new NotificationRequest();
        request.setRecords(Collections.singletonList(record));
        return request;
    }
}
