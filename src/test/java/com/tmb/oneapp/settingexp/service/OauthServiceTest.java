package com.tmb.oneapp.settingexp.service;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.client.OauthFeignClient;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.commonauthen.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.settingexp.model.commonauthen.CommonAuthenVerifyRefResponse;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class OauthServiceTest {
    @InjectMocks
    OauthService oauthService;

    @Mock
    OauthFeignClient oauthFeignClient;

    HttpHeaders headers = new HttpHeaders();
    String correlationId = "correlation-id";
    String crmId = "crm-id";
    String ipAddress = "127.0.0.1";

    @BeforeEach
    void setUp() {
        headers.add(SettingExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.add(SettingExpConstant.HEADER_CRM_ID, crmId);
        headers.add(SettingExpConstant.HEADER_IP_ADDRESS, ipAddress);
    }

    @Test
    void testIsVerifyCommonAuthenPass_ShouldSuccess() {
        mockVerifyCommonAuthenReturnSuccess();

        boolean actual = oauthService.isVerifyCommonAuthenPass(headers, new CommonAuthenVerifyRefRequest());

        Assertions.assertTrue(actual);
    }

    @Test
    void testIsVerifyCommonAuthenPass_WhenRequireHeaderIncorrect_ShouldReturnFalse() {
        headers.remove(SettingExpConstant.HEADER_CRM_ID);

        boolean actual = oauthService.isVerifyCommonAuthenPass(headers, new CommonAuthenVerifyRefRequest());

        Assertions.assertFalse(actual);
    }

    @Test
    void testIsVerifyCommonAuthenPass_WhenVerifyCommonAuthenFeignException_ShouldReturnFalse() {
        mockVerifyCommonAuthenFeignException();

        boolean actual = oauthService.isVerifyCommonAuthenPass(headers, new CommonAuthenVerifyRefRequest());

        Assertions.assertFalse(actual);
    }

    private void mockVerifyCommonAuthenFeignException() {
        Mockito.when(oauthFeignClient.verifyCommonAuthen(Mockito.any(HttpHeaders.class), Mockito.any(CommonAuthenVerifyRefRequest.class))).thenThrow(FeignException.class);
    }

    private void mockVerifyCommonAuthenReturnSuccess() {
        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(SettingServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new CommonAuthenVerifyRefResponse());
        Mockito.when(oauthFeignClient.verifyCommonAuthen(Mockito.any(HttpHeaders.class), Mockito.any(CommonAuthenVerifyRefRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }
}