package com.tmb.oneapp.settingexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.CommonData;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.settingexp.client.CommonServiceClient;
import com.tmb.oneapp.settingexp.client.CustomerServiceClient;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.CustomerCrmProfile;
import com.tmb.oneapp.settingexp.model.activitylog.PinFreeActivityEvent;
import com.tmb.oneapp.settingexp.model.pinfree.CommonAuthenticationInformation;
import com.tmb.oneapp.settingexp.model.pinfree.PinFreeResponse;
import com.tmb.oneapp.settingexp.model.pinfree.UpdatePinFreeSettingsRequest;
import com.tmb.oneapp.settingexp.utils.AsyncUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.Collections;

import static com.tmb.oneapp.settingexp.utils.TestUtils.buildSuccessfulResponse;
import static com.tmb.oneapp.settingexp.utils.TestUtils.failedFuture;
import static com.tmb.oneapp.settingexp.utils.TestUtils.stubAsyncUtilsExecuteMethodAsync;
import static com.tmb.oneapp.settingexp.utils.TestUtils.stubAsyncUtilsExecuteMethodAsyncWithSupplier;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PinFreeServiceImplTest {

    private static final String CORRELATION_ID = "corr-id";
    private static final String CRM_ID = "crm-id";

    @Mock
    private CommonServiceClient commonServiceClient;

    @Mock
    private CustomerServiceClient customerServiceClient;

    @Mock
    private AsyncUtils asyncUtils;

    @Mock
    private EventService eventService;

    @InjectMocks
    private PinFreeServiceImpl pinFreeService;

    @Test
    void test_getPinFreeData_ShouldReturnExpectedResponse() throws TMBCommonException {
        CommonData commonData = buildCommonData("2000", "4000");
        CustomerCrmProfile profile = buildCustomerProfile("true", BigDecimal.valueOf(500));

        stubAsyncUtilsExecuteMethodAsyncWithSupplier(asyncUtils);

        when(commonServiceClient.getCommonConfigByModule(SettingExpConstant.COMMON_MODULE))
                .thenReturn(buildSuccessfulResponse(Collections.singletonList(commonData)));
        when(customerServiceClient.fetchCustomerCrmProfile(CORRELATION_ID, CRM_ID))
                .thenReturn(buildSuccessfulResponse(profile));

        PinFreeResponse result = pinFreeService.getPinFreeData(CORRELATION_ID, CRM_ID);

        Assertions.assertTrue(result.getPinFreeEnableFlag());
        Assertions.assertEquals(new BigDecimal("2000"), result.getPinFreeMaxLimit());
        Assertions.assertEquals(new BigDecimal("4000"), result.getPinFreeMaxAccum());
        Assertions.assertEquals(BigDecimal.valueOf(500), result.getPinFreeQrLimit());
        Assertions.assertTrue(result.getIsRequireCommonAuthen());

        CommonAuthenticationInformation authInfo = result.getCommonAuthenticationInformation();
        Assertions.assertNotNull(authInfo);
        Assertions.assertEquals(SettingExpConstant.PIN_FREE_FEATURE_ID, authInfo.getFeatureId());
        Assertions.assertEquals(BigDecimal.ZERO, authInfo.getAmount());
        Assertions.assertEquals(SettingExpConstant.PIN_FREE_FLOW, authInfo.getFlow());
        Assertions.assertEquals(BigDecimal.ZERO, authInfo.getDailyAmount());
    }

    @Test
    void test_getPinFreeData_WhenQrLimitMissing_ShouldDefaultToStandardLimit() throws TMBCommonException {
        CommonData commonData = buildCommonData("1500", "2500");
        CustomerCrmProfile profile = buildCustomerProfile("false", null);

        stubAsyncUtilsExecuteMethodAsyncWithSupplier(asyncUtils);

        when(commonServiceClient.getCommonConfigByModule(SettingExpConstant.COMMON_MODULE))
                .thenReturn(buildSuccessfulResponse(Collections.singletonList(commonData)));
        when(customerServiceClient.fetchCustomerCrmProfile(CORRELATION_ID, CRM_ID))
                .thenReturn(buildSuccessfulResponse(profile));

        PinFreeResponse result = pinFreeService.getPinFreeData(CORRELATION_ID, CRM_ID);

        Assertions.assertEquals(new BigDecimal("1500"), result.getPinFreeMaxLimit());
        Assertions.assertEquals(new BigDecimal("2500"), result.getPinFreeMaxAccum());
        Assertions.assertEquals(BigDecimal.valueOf(1000), result.getPinFreeQrLimit());
        Assertions.assertFalse(result.getPinFreeEnableFlag());
    }

    @Test
    void test_getPinFreeData_WhenCommonConfigMissing_ShouldThrowException() {
        CustomerCrmProfile profile = buildCustomerProfile("true", BigDecimal.valueOf(500));

        stubAsyncUtilsExecuteMethodAsyncWithSupplier(asyncUtils);

        when(commonServiceClient.getCommonConfigByModule(SettingExpConstant.COMMON_MODULE))
                .thenReturn(buildSuccessfulResponse(Collections.emptyList()));
        when(customerServiceClient.fetchCustomerCrmProfile(CORRELATION_ID, CRM_ID))
                .thenReturn(buildSuccessfulResponse(profile));

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> pinFreeService.getPinFreeData(CORRELATION_ID, CRM_ID));

        Assertions.assertEquals(ResponseCode.PIN_FREE_CONFIG_NOT_FOUND.getCode(), exception.getErrorCode());
    }

    @Test
    void test_getPinFreeData_WhenProfileMissing_ShouldThrowException() {
        CommonData commonData = buildCommonData("2000", "4000");

        stubAsyncUtilsExecuteMethodAsyncWithSupplier(asyncUtils);

        when(commonServiceClient.getCommonConfigByModule(SettingExpConstant.COMMON_MODULE))
                .thenReturn(buildSuccessfulResponse(Collections.singletonList(commonData)));
        when(customerServiceClient.fetchCustomerCrmProfile(CORRELATION_ID, CRM_ID))
                .thenReturn(buildSuccessfulResponse(null));

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> pinFreeService.getPinFreeData(CORRELATION_ID, CRM_ID));

        Assertions.assertEquals(ResponseCode.PIN_FREE_DATA_NOT_FOUND.getCode(), exception.getErrorCode());
    }

    @Test
    void test_getPinFreeData_WhenAsyncExecutionFails_ShouldWrapException() {
        stubAsyncUtilsExecuteMethodAsync(asyncUtils,
                failedFuture(new RuntimeException("boom")),
                new CustomerCrmProfile());

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> pinFreeService.getPinFreeData(CORRELATION_ID, CRM_ID));

        Assertions.assertEquals(ResponseCode.PIN_FREE_SERVICE_UNAVAILABLE.getCode(), exception.getErrorCode());
    }

    @Test
    void test_updatePinFreeSettings_ShouldReturnSuccessMessage() throws TMBCommonException {
        UpdatePinFreeSettingsRequest request = new UpdatePinFreeSettingsRequest(true, BigDecimal.valueOf(800));

        when(customerServiceClient.updatePinFreeSettings(CRM_ID, CORRELATION_ID, request))
                .thenReturn(buildSuccessfulResponse("success"));
        when(eventService.publish(any())).thenReturn(true);

        String result = pinFreeService.updatePinFreeSettings(CORRELATION_ID, CRM_ID, request);

        Assertions.assertEquals("success", result);

        ArgumentCaptor<PinFreeActivityEvent> eventCaptor = ArgumentCaptor.forClass(PinFreeActivityEvent.class);
        verify(eventService).publish(eventCaptor.capture());
        PinFreeActivityEvent activityEvent = eventCaptor.getValue();
        Assertions.assertEquals(SettingExpConstant.PIN_FREE_ACTIVITY_STATUS_ON, activityEvent.getPinFreeFlag());
        Assertions.assertEquals("800", activityEvent.getScanQr());
    }

    @Test
    void test_updatePinFreeSettings_WhenDisabled_ShouldPublishOffActivity() throws TMBCommonException {
        UpdatePinFreeSettingsRequest request = new UpdatePinFreeSettingsRequest(false, null);

        when(customerServiceClient.updatePinFreeSettings(CRM_ID, CORRELATION_ID, request))
                .thenReturn(buildSuccessfulResponse("disabled"));
        when(eventService.publish(any())).thenReturn(true);

        String result = pinFreeService.updatePinFreeSettings(CORRELATION_ID, CRM_ID, request);

        Assertions.assertEquals("disabled", result);

        ArgumentCaptor<PinFreeActivityEvent> eventCaptor = ArgumentCaptor.forClass(PinFreeActivityEvent.class);
        verify(eventService).publish(eventCaptor.capture());
        PinFreeActivityEvent activityEvent = eventCaptor.getValue();
        Assertions.assertEquals(SettingExpConstant.PIN_FREE_ACTIVITY_STATUS_OFF, activityEvent.getPinFreeFlag());
        Assertions.assertEquals(SettingExpConstant.PIN_FREE_SCAN_QR_NOT_AVAILABLE, activityEvent.getScanQr());
    }

    @Test
    void test_updatePinFreeSettings_WhenRequestIsNull_ShouldThrowException() {
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> pinFreeService.updatePinFreeSettings(CORRELATION_ID, CRM_ID, null));

        Assertions.assertEquals(ResponseCode.MISSING_REQUIRED_FIELD.getCode(), exception.getErrorCode());
        verifyNoInteractions(eventService);
    }

    @Test
    void test_updatePinFreeSettings_WhenDownstreamReturnsNullData_ShouldThrowException() {
        UpdatePinFreeSettingsRequest request = new UpdatePinFreeSettingsRequest(false, BigDecimal.valueOf(500));

        TmbServiceResponse<String> body = new TmbServiceResponse<>();
        body.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
        ResponseEntity<TmbServiceResponse<String>> response = ResponseEntity.ok(body);

        when(customerServiceClient.updatePinFreeSettings(CRM_ID, CORRELATION_ID, request))
                .thenReturn(response);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> pinFreeService.updatePinFreeSettings(CORRELATION_ID, CRM_ID, request));

        Assertions.assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
        verifyNoInteractions(eventService);
    }

    private CommonData buildCommonData(String maxLimit, String maxAccum) {
        CommonData commonData = new CommonData();
        commonData.setPinFreeMaxLimit(maxLimit);
        commonData.setPinFreeMaxAccum(maxAccum);
        return commonData;
    }

    private CustomerCrmProfile buildCustomerProfile(String enableFlag, BigDecimal qrLimit) {
        CustomerCrmProfile profile = new CustomerCrmProfile();
        profile.setPinFreeSeetingFlag(enableFlag);
        profile.setPinFreeQrLimit(qrLimit);
        return profile;
    }
}
