package com.tmb.oneapp.settingexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.settingexp.client.CustomerAccountBizServiceFeignClient;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import com.tmb.oneapp.settingexp.constant.SettingExpConstant;
import com.tmb.oneapp.settingexp.model.commonauthen.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.settingexp.model.manageviewonly.EnableViewOnlyRequest;
import com.tmb.oneapp.settingexp.model.manageviewonly.UpdateAccountViewOnlyRequest;
import com.tmb.oneapp.settingexp.utils.SettingServiceUtils;
import feign.FeignException;
import feign.Request;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ViewOnlyServiceImplTest {
    @InjectMocks
    ViewOnlyServiceImpl viewOnlyServiceImpl;

    @Mock
    CustomerAccountBizServiceFeignClient customerAccountBizServiceFeignClient;

    @Mock
    OauthService oauthService;

    HttpHeaders headers;

    @BeforeEach
    void setUp() {
        headers = new HttpHeaders();
        headers.set(SettingExpConstant.HEADER_CRM_ID, "crmId");
        headers.set(SettingExpConstant.HEADER_CORRELATION_ID, "correlationId");
        headers.set(SettingExpConstant.HEADER_ACCEPT_LANGUAGE, "th");
        headers.set(SettingExpConstant.HEADER_APP_VERSION, "4.0.0");
        headers.set(SettingExpConstant.HEADER_IP_ADDRESS, "ipAddress");
    }

    @Test
    void testEnableViewOnly_ShouldSuccess() {
        EnableViewOnlyRequest request = new EnableViewOnlyRequest();
        request.setAccountNumber("**********");
        request.setFrUuid("fr-uuid");
        mockIsVerifyCommonAuthenPassReturnPass();
        mockUpdateAccountViewOnlySuccess();

        Assertions.assertDoesNotThrow(() -> viewOnlyServiceImpl.enableViewOnly(headers, request));
    }

    @Test
    void testEnableViewOnly_WhenValidateCommonAuthenFailed_ShouldThrowTmbCommonException() {
        EnableViewOnlyRequest request = new EnableViewOnlyRequest();
        request.setAccountNumber("**********");
        request.setFrUuid("fr-uuid");
        mockIsVerifyCommonAuthenPassReturnFalse();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> viewOnlyServiceImpl.enableViewOnly(headers, request));

        Assertions.assertEquals(ResponseCode.VERIFY_COMMON_AUTHENTICATION_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testEnableViewOnly_WhenUpdateAccountViewOnlyFailed_ShouldThrowTmbCommonException() throws JsonProcessingException {
        EnableViewOnlyRequest request = new EnableViewOnlyRequest();
        request.setAccountNumber("**********");
        request.setFrUuid("fr-uuid");
        mockIsVerifyCommonAuthenPassReturnPass();
        mockUpdateAccountViewOnlyFeignExceptionWithCustomError();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> viewOnlyServiceImpl.enableViewOnly(headers, request));

        Assertions.assertEquals("custom_error", exception.getErrorCode());
    }

    private void mockUpdateAccountViewOnlyFeignExceptionWithCustomError() throws JsonProcessingException {
        Status status = new Status();
        status.setCode("custom_error");
        TmbServiceResponse<?> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(status);

        Request feignRequest = Request.create(Request.HttpMethod.PATCH, "/v1/customer-account-biz/manage-account/view-only", new HashMap<>(), null, null, null);
        FeignException.InternalServerError customError = new FeignException.InternalServerError("failed", feignRequest, TMBUtils.convertJavaObjectToString(tmbServiceResponse).getBytes(), new HashMap<>());
        when(customerAccountBizServiceFeignClient.enableViewOnly(any(UpdateAccountViewOnlyRequest.class), any(HttpHeaders.class))).thenThrow(customError);
    }

    private void mockUpdateAccountViewOnlySuccess() {
        TmbServiceResponse<String> t = new TmbServiceResponse<>();
        t.setStatus(SettingServiceUtils.getStatusSuccess());
        t.setData("Success");
        when(customerAccountBizServiceFeignClient.enableViewOnly(any(UpdateAccountViewOnlyRequest.class), any(HttpHeaders.class))).thenReturn(ResponseEntity.ok(t));
    }

    private void mockIsVerifyCommonAuthenPassReturnPass() {
        when(oauthService.isVerifyCommonAuthenPass(any(HttpHeaders.class), any(CommonAuthenVerifyRefRequest.class))).thenReturn(true);
    }

    private void mockIsVerifyCommonAuthenPassReturnFalse() {
        when(oauthService.isVerifyCommonAuthenPass(any(HttpHeaders.class), any(CommonAuthenVerifyRefRequest.class))).thenReturn(false);
    }
}