package com.tmb.oneapp.settingexp.utils;

import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.settingexp.constant.ResponseCode;
import org.mockito.stubbing.Answer;
import org.springframework.http.ResponseEntity;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;

/**
 * Shared helpers for test double and asynchronous utility setup.
 */
public final class TestUtils {

    private TestUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * Configures {@link AsyncUtils#executeMethodAsync(java.util.function.Supplier)} to return
     * a deterministic sequence of {@link CompletableFuture} results.
     * <p>
     * Each provided response is converted to a {@link CompletableFuture} unless it is already one.
     * Supplying a {@link Throwable} results in a failed future. When the underlying code performs
     * more calls than provided responses, an {@link IllegalStateException} is thrown to highlight the
     * mismatch.
     *
     * @param asyncUtils the async utility mock to configure
     * @param responses  ordered responses to return for consecutive invocations
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static void stubAsyncUtilsExecuteMethodAsync(AsyncUtils asyncUtils, Object... responses) {
        AtomicInteger callIndex = new AtomicInteger();
        doAnswer((Answer) invocation -> {
            if (responses == null || responses.length == 0) {
                throw new IllegalStateException("No responses provided for AsyncUtils stub");
            }

            int current = callIndex.getAndIncrement();
            if (current >= responses.length) {
                throw new IllegalStateException("More AsyncUtils calls than configured responses");
            }

            Object response = responses[current];

            return toCompletableFuture(response);
        }).when(asyncUtils).executeMethodAsync(any());
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static void stubAsyncUtilsExecuteMethodAsyncWithSupplier(AsyncUtils asyncUtils) {
        doAnswer((Answer) invocation -> {
            Supplier supplier = invocation.getArgument(0);
            try {
                Object result = supplier.get();
                return CompletableFuture.completedFuture(result);
            } catch (Exception ex) {
                CompletableFuture<Object> future = new CompletableFuture<>();
                future.completeExceptionally(ex);
                return future;
            }
        }).when(asyncUtils).executeMethodAsync(any());
    }

    /**
     * Creates a successful {@link ResponseEntity} carrying the provided payload.
     *
     * @param data payload to embed within the response body
     * @return response entity wrapping a {@link TmbServiceResponse}
     */
    public static <T> ResponseEntity<TmbServiceResponse<T>> buildSuccessfulResponse(T data) {
        TmbServiceResponse<T> body = new TmbServiceResponse<>();
        body.setData(data);
        body.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
        return ResponseEntity.ok(body);
    }

    /**
     * Creates a successful {@link ResponseEntity} carrying the provided payload using the
     * {@link TmbOneServiceResponse} envelope.
     *
     * @param data payload to embed within the response body
     * @return response entity wrapping a {@link TmbOneServiceResponse}
     */
    public static <T> ResponseEntity<TmbOneServiceResponse<T>> buildSuccessfulOneServiceResponse(T data) {
        TmbOneServiceResponse<T> body = new TmbOneServiceResponse<>();
        body.setData(data);
        body.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        return ResponseEntity.ok(body);
    }

    /**
     * Creates a {@link CompletableFuture} that has already completed exceptionally.
     *
     * @param throwable the failure to propagate
     * @param <T>       the future payload type
     * @return failed future containing the provided throwable
     */
    public static <T> CompletableFuture<T> failedFuture(Throwable throwable) {
        Objects.requireNonNull(throwable, "throwable");
        CompletableFuture<T> future = new CompletableFuture<>();
        future.completeExceptionally(throwable);
        return future;
    }

    private static CompletableFuture<?> toCompletableFuture(Object response) {
        if (response instanceof CompletableFuture<?> future) {
            return future;
        }

        if (response instanceof Throwable throwable) {
            return failedFuture(throwable);
        }

        return CompletableFuture.completedFuture(response);
    }
}
